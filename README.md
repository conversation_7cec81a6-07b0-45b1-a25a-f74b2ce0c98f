# 🌟 DreamVault

A comprehensive full-stack bucket list application that helps users track, manage, and achieve their dreams and goals. Built with modern technologies and designed for scalability, security, and user experience.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14+-blue.svg)](https://www.postgresql.org/)

## 🚀 Features

### 🎯 Core Functionality
- **Bucket List Management** - Create, organize, and track life goals and dreams
- **Progress Tracking** - Document your journey with photos, notes, and milestones
- **Smart Categorization** - Organize items by travel, adventure, career, personal, creative, fitness, education, and social
- **Priority System** - Set priorities from low to urgent for better goal management
- **Status Tracking** - Monitor progress from not started to completed

### 🔐 Authentication & Security
- **Supabase Auth** - Secure authentication across all platforms
- **Row Level Security** - Database-level security with RLS policies
- **Role-based Access** - User profile management and permissions
- **Input Sanitization** - XSS protection and data validation
- **Rate Limiting** - API protection against abuse

### 📱 Multi-Platform Support
- **Web Application** - Modern React-based web interface with dark/light theme support
- **Mobile Apps** - Native iOS and Android apps with React Native + Expo
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Theme System** - Complete dark/light/system theme support with persistence
- **Offline Support** - Work on your goals even without internet connection

### 🤖 AI-Powered Features
- **Smart Suggestions** - AI-powered goal recommendations
- **Progress Insights** - Intelligent analysis of your achievements
- **Goal Planning** - AI assistance for breaking down complex goals

### 🌐 Social & Sharing
- **List Sharing** - Share bucket lists with friends and family
- **Achievement Notifications** - Celebrate milestones together
- **Social Feed** - See friends' achievements and progress

### 📊 Analytics & Insights
- **Progress Analytics** - Visual insights into your goal completion
- **Achievement System** - Gamified experience with badges and rewards
- **Timeline View** - Visual progress tracking over time
- **Export Options** - Export your data in various formats
- **Settings Management** - Comprehensive user preferences and privacy controls

### 🎨 User Experience
- **Modern UI Design** - Beautiful, intuitive interface with glassmorphism effects
- **Custom Animations** - Smooth transitions and micro-interactions
- **Accessibility** - WCAG compliant components with keyboard navigation
- **Performance Optimized** - Fast loading times and smooth interactions

## 🏗️ Architecture

### Technology Stack

**Backend API**
- **Express.js** - Fast, unopinionated web framework
- **TypeScript** - Type-safe JavaScript development
- **Supabase** - PostgreSQL database with real-time capabilities
- **Supabase Auth** - Authentication and authorization
- **Socket.io** - Real-time bidirectional communication
- **AWS S3** - Cloud storage for media files
- **Redis** - In-memory caching and session storage
- **Sharp** - Image processing and optimization

**Web Application**
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Supabase** - Direct database integration with SSR support
- **Tailwind CSS** - Utility-first CSS framework with dark mode support
- **Radix UI** - Accessible UI components
- **React Hook Form** - Performant forms with validation
- **Theme Provider** - Custom theme switching with localStorage persistence

**Mobile Applications**
- **React Native** - Cross-platform mobile development
- **Expo SDK ~53** - Development platform with new architecture
- **React Navigation** - Navigation library
- **Supabase** - Direct database and auth integration
- **Expo Camera** - Camera and media capture
- **Expo Image Picker** - Media selection and upload

**Shared Infrastructure**
- **TypeScript Types** - Shared type definitions across platforms
- **ESLint & Prettier** - Code linting and formatting
- **GitHub Actions** - CI/CD pipeline
- **Sentry** - Error monitoring and performance tracking

### Project Structure

```
DreamVault/
├── dreamvault-backend/      # Express.js API server
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── lib/            # Utilities and services
│   │   ├── middleware/     # Express middleware
│   │   └── database/       # Supabase SQL setup and storage
│   └── package.json
├── dreamvault-web-app/     # Next.js web application
│   ├── src/
│   │   ├── app/            # App Router pages
│   │   ├── components/     # React components (including theme provider)
│   │   ├── lib/            # Utilities and hooks
│   │   └── types/          # Web-specific type definitions
│   └── package.json
├── dreamvault-mobile-app/  # React Native + Expo app
│   ├── src/
│   │   ├── screens/        # Screen components
│   │   ├── navigation/     # Navigation setup
│   │   ├── components/     # Reusable components
│   │   └── utils/          # Utilities and services
│   └── package.json
├── packages/
│   └── types/              # Shared TypeScript types
└── CLAUDE.md              # Development guidance for AI assistants
```

## 🛠️ Installation & Setup

### Prerequisites

- **Node.js** 18+ and npm
- **Supabase Account** for database and authentication
- **Redis** (optional, for caching)
- **AWS Account** (for S3 storage)
- **OpenAI API Key** (for AI features)
- **Expo CLI** (for mobile development)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Stijnus/DreamVault.git
   cd DreamVault
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd dreamvault-backend && npm install

   # Web
   cd ../dreamvault-web-app && npm install

   # Mobile
   cd ../dreamvault-mobile-app && npm install
   
   # Shared types
   cd ../packages/types && npm install
   ```

3. **Set up environment variables**
   ```bash
   # Backend
   cd dreamvault-backend && cp .env.example .env
   # Edit .env with your Supabase, Redis, AWS S3, and OpenAI values

   # Web
   cd ../dreamvault-web-app && cp .env.local.example .env.local
   # Edit .env.local with your Supabase URL and anon key

   # Mobile
   cd ../dreamvault-mobile-app && cp .env.example .env
   # Edit .env with your Supabase URL and backend API URL
   ```

4. **Set up the database and storage**
   ```bash
   cd dreamvault-backend
   npm run setup:storage  # Setup Supabase storage buckets and policies
   # Run SQL setup in Supabase dashboard from database/setup-storage.sql
   ```

5. **Start development servers**
   ```bash
   # Terminal 1 - Backend API (port 3001)
   cd dreamvault-backend && npm run dev

   # Terminal 2 - Web App (port 3000)
   cd dreamvault-web-app && npm run dev

   # Terminal 3 - Mobile App
   cd dreamvault-mobile-app && npm run start
   ```

### Environment Configuration

#### Backend (.env)
```bash
# Server
PORT=3001
NODE_ENV=development

# Supabase
SUPABASE_URL=your-supabase-project-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis (optional)
REDIS_URL=redis://localhost:6379

# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=dreamvault-media

# External APIs
OPENAI_API_KEY=your-openai-api-key
```

#### Web (.env.local)
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

#### Mobile (.env)
```bash
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your-supabase-project-url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

## 📱 Development

### Backend Development

```bash
cd dreamvault-backend

# Development server with hot reload
npm run dev

# Build for production
npm run build

# Database and storage operations
npm run setup:storage  # Setup Supabase storage buckets and policies

# Code quality
npm run lint           # ESLint
npm run type-check     # TypeScript checking
```

### Web Development

```bash
cd dreamvault-web-app

# Development server (port 3000)
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Code quality
npm run lint           # Next.js ESLint
npm run type-check     # TypeScript checking
```

### Mobile Development

```bash
cd dreamvault-mobile-app

# Start Expo development server
npm run start

# Run on specific platforms
npm run android        # Expo Android build
npm run ios            # Expo iOS build
npm run web            # Expo web build
```

## 🧪 Testing

### Backend Testing
```bash
cd dreamvault-backend

# Health check endpoint
curl http://localhost:3001/api/health

# Test Supabase connection and services
# Currently using manual testing - automated tests to be implemented
```

### Integration Testing
- Manual API endpoint testing
- Supabase connection testing via health endpoints
- File upload and media processing verification
- AI service integration testing

## 🚀 Deployment

### Backend Deployment

**Prerequisites for Production:**
- Supabase project with production database
- Redis instance (AWS ElastiCache, Redis Cloud, etc.)
- AWS S3 bucket for media storage
- OpenAI API key for AI features
- Environment variables configured

**Docker Support:**
```bash
cd dreamvault-backend
docker build -t dreamvault-api .
docker run -p 3001:3001 dreamvault-api
```

### Web Application Deployment

**Vercel (Recommended):**
```bash
cd dreamvault-web-app
npm run build
# Deploy to Vercel
```

**Docker:**
```bash
cd dreamvault-web-app
docker build -t dreamvault-web .
docker run -p 3000:3000 dreamvault-web
```

### Mobile App Deployment

**iOS App Store:**
```bash
cd dreamvault-mobile-app
expo build:ios
# Follow Expo's guide for App Store submission
```

**Google Play Store:**
```bash
cd dreamvault-mobile-app
expo build:android
# Follow Expo's guide for Play Store submission
```

## 📚 API Documentation

### AI Services Endpoints
- `POST /api/ai/suggestions` - Get AI-powered goal suggestions
- `POST /api/ai/analyze` - Analyze goals and provide insights
- `POST /api/ai/generate` - Generate goal details and planning
- `GET /api/ai/status` - Check AI service status

### Health & Monitoring
- `GET /api/health` - Comprehensive health check with Supabase connection
- `GET /api/docs` - API documentation

### Note
Authentication and bucket list operations are handled directly through Supabase on the frontend, with the backend providing AI services and real-time features.

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests if applicable
4. **Run tests**: `npm test` in relevant directories
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Ensure all linters pass
- Update documentation as needed

### Code Style

- **ESLint** and **Prettier** are configured for consistent code style
- **TypeScript** strict mode is enabled
- Follow component naming conventions
- Use meaningful variable and function names

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the individual README files in each directory
- **Issues**: [GitHub Issues](https://github.com/Stijnus/DreamVault/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Stijnus/DreamVault/discussions)

## 🎯 Roadmap

### Phase 1 (Completed)
- ✅ Core bucket list functionality
- ✅ User authentication and profiles
- ✅ Mobile app support with React Native + Expo
- ✅ File upload and media management
- ✅ Complete dark/light theme system
- ✅ Settings and user preferences
- ✅ Responsive web interface with modern UI

### Phase 2 (In Progress)
- ✅ AI-powered goal suggestions (implemented)
- 🔄 Advanced analytics and insights (partial)
- 🔄 Social features and list sharing
- 🔄 Push notifications

### Phase 3
- 📋 Collaborative bucket lists
- 📋 Integration with calendar apps
- 📋 Location-based reminders
- 📋 Goal achievement streaks

### Phase 4
- 📋 Community features
- 📋 Goal marketplace
- 📋 Advanced AI insights
- 📋 Wearable device integration

## 🙏 Acknowledgments

- **Supabase** for the comprehensive backend-as-a-service platform
- **Next.js** team for the amazing framework
- **Expo** for making mobile development accessible
- **OpenAI** for AI capabilities
- **Open source community** for the amazing tools and libraries

---

**Made with ❤️ by the DreamVault team**

*Turn your dreams into achievable goals, one step at a time.*