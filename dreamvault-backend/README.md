# DreamVault Backend API Server

This is the backend API server for the DreamVault bucket list application. It provides a robust, scalable REST API with real-time capabilities, comprehensive monitoring, and security features.

## 🚀 Features

- **Express.js Server** with TypeScript
- **Comprehensive Security** with Helmet, CORS, rate limiting, and input sanitization
- **Authentication** via Auth0 with JWT validation
- **Database** PostgreSQL with Prisma ORM
- **Real-time Features** with Socket.io (planned)
- **Monitoring & Health Checks** with custom metrics and logging
- **Error Handling** with structured error responses
- **Environment Configuration** with validation
- **Graceful Shutdown** handling
- **Comprehensive Testing** with Jest and Supertest

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- Auth0 account (for authentication)
- Redis (optional, for caching)

## 🛠️ Setup

1. **Install dependencies:**
```bash
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your actual values
```

3. **Set up the database:**
```bash
npm run db:push
npm run db:seed
```

4. **Start the development server:**
```bash
npm run dev
```

The server will start on `http://localhost:3001` with the following endpoints available:
- 📊 Health check: `http://localhost:3001/health`
- 🔗 API endpoint: `http://localhost:3001/api`
- 📈 Metrics: `http://localhost:3001/metrics`

## 📜 Available Scripts

### Development
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run type-check` - Run TypeScript type checking
- `npm run lint` - Run ESLint

### Database
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with sample data
- `npm run db:studio` - Open Prisma Studio
- `npm run db:reset` - Reset database (⚠️ destructive)

### Testing
- `npm test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### Utilities
- `npm run test:auth` - Test Auth0 connection
- `npm run test:db` - Test database connection

## 🔌 API Endpoints

### Health & Monitoring
- `GET /health` - Comprehensive health check with service status
- `GET /ready` - Kubernetes readiness probe
- `GET /live` - Kubernetes liveness probe  
- `GET /metrics` - Application and system metrics

### Authentication (`/api/auth`)
- `POST /api/auth/register` - Complete user registration after Auth0 signup
- `POST /api/auth/login` - User login validation
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Initiate password reset
- `GET /api/auth/me` - Get current user profile
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/preferences` - Update user preferences
- `DELETE /api/auth/account` - Delete user account

### Bucket List Management (`/api/items`)
- `GET /api/items` - Get user's bucket list items with filtering and pagination
- `POST /api/items` - Create new bucket list item
- `GET /api/items/:id` - Get specific bucket list item
- `PUT /api/items/:id` - Update bucket list item
- `DELETE /api/items/:id` - Delete bucket list item

### Progress Tracking (`/api/progress`)
- `POST /api/items/:id/progress` - Add progress entry to item
- `GET /api/items/:id/progress` - Get progress history for item
- `PUT /api/progress/:id` - Update progress entry
- `DELETE /api/progress/:id` - Delete progress entry

### Media Management (`/api/media`)
- `POST /api/media/upload` - Upload media files (images, documents)
- `GET /api/media/:id` - Get media file details
- `DELETE /api/media/:id` - Delete media file

### Search & Discovery (`/api/search`)
- `GET /api/search` - Full-text search across bucket list items
- `GET /api/discovery` - Explore public bucket list items
- `POST /api/search/index` - Reindex search data (admin)

### Social Features (`/api/shared-lists`)
- `POST /api/shared-lists` - Share bucket list with others
- `GET /api/shared-lists` - Get user's shared lists
- `PUT /api/shared-lists/:id/members` - Manage list members
- `DELETE /api/shared-lists/:id` - Remove list sharing

### Analytics (`/api/analytics`)
- `GET /api/analytics/progress` - Get progress analytics
- `GET /api/analytics/achievements` - Get achievement statistics
- `GET /api/analytics/trends` - Get goal completion trends

### AI Features (`/api/ai`)
- `POST /api/ai/suggestions` - Get AI-powered goal suggestions
- `POST /api/ai/analyze` - Analyze progress and provide insights

### Notifications (`/api/notifications`)
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/:id/read` - Mark notification as read
- `POST /api/notifications/settings` - Update notification preferences

### API Information
- `GET /api` - API version and endpoint information

## 🔧 Configuration

The server uses a comprehensive configuration system with environment validation. Key configuration areas:

### Required Environment Variables
```bash
DATABASE_URL=postgresql://username:password@localhost:5432/dreamvault_dev
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_AUDIENCE=your-api-identifier
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret
JWT_SECRET=your-jwt-secret-key
```

### Optional Environment Variables
```bash
PORT=3001
NODE_ENV=development
REDIS_URL=redis://localhost:6379
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=dreamvault-media
OPENAI_API_KEY=your-openai-api-key
WEATHER_API_KEY=your-weather-api-key
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### Security Configuration
```bash
RATE_LIMIT_MAX=1000              # Max requests per window
RATE_LIMIT_WINDOW_MS=900000      # Rate limit window (15 minutes)
MAX_FILE_SIZE=10485760           # Max file size (10MB)
BCRYPT_ROUNDS=12                 # BCrypt hash rounds
```

### Monitoring Configuration
```bash
ENABLE_METRICS=true              # Enable metrics collection
ENABLE_HEALTH_CHECKS=true        # Enable health checks
LOG_LEVEL=info                   # Logging level (error, warn, info, debug)
```

## 🏗️ Architecture

### Server Structure
```
src/
├── config/           # Configuration management
├── lib/             # Utilities and shared libraries
│   ├── auth.ts      # Authentication utilities
│   ├── monitoring.ts # Monitoring and logging
│   └── prisma.ts    # Database client
├── middleware/      # Express middleware
│   └── auth.ts      # Auth0 JWT validation
├── routes/          # API route handlers
│   ├── auth.ts      # Authentication routes
│   └── index.ts     # Route aggregation
├── tests/           # Test files
└── index.ts         # Main server file
```

### Key Components

**Configuration System (`src/config/`)**
- Environment validation
- Type-safe configuration object
- Development vs production settings

**Library Services (`src/lib/`)**
- **Authentication** (`auth.ts`) - Auth0 JWT validation and user management
- **Database** (`prisma.ts`) - Database client and connection management
- **Monitoring** (`monitoring.ts`) - Request/response metrics and health checks
- **Media Service** (`media-service.ts`) - File upload and media management
- **AI Service** (`ai-service.ts`) - OpenAI integration for smart suggestions
- **Notification Service** (`notification-service.ts`) - Push notifications and alerts
- **Real-time Service** (`realtime-service.ts`) - Socket.io server implementation
- **Error Handler** (`error-handler.ts`) - Centralized error handling and logging

**Route Handlers (`src/routes/`)**
- **Authentication** (`auth.ts`) - User authentication and profile management
- **Items** (`items.ts`) - Bucket list CRUD operations
- **Progress** (`progress.ts`) - Progress tracking and analytics
- **Media** (`media.ts`) - File upload and management endpoints
- **Search** (`search.ts`, `fulltext-search.ts`) - Search functionality
- **Social** (`shared-lists.ts`, `discovery.ts`) - Social and sharing features
- **AI** (`ai.ts`) - AI-powered suggestions and insights
- **Analytics** (`analytics.ts`, `progress-analytics.ts`) - Progress and goal analytics
- **Notifications** (`notifications.ts`) - Notification management

**Middleware (`src/middleware/`)**
- **Authentication** (`auth.ts`) - JWT validation and user context
- **Error Handling** - Global error processing and logging
- **Rate Limiting** - API abuse prevention
- **Request Validation** - Input sanitization and validation

## 🔒 Security Features

- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Prevent abuse
- **Input Sanitization** - XSS protection
- **JWT Validation** - Secure authentication
- **Request ID Tracking** - Request tracing
- **Error Handling** - No sensitive data leakage

## 📊 Monitoring & Observability

### Health Checks
The `/health` endpoint provides comprehensive service status:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "development",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "auth0": "connected"
  },
  "memory": {
    "status": "healthy",
    "details": {
      "heapUsedMB": 45,
      "heapTotalMB": 67,
      "usagePercentage": 67
    }
  }
}
```

### Metrics
The `/metrics` endpoint provides application and system metrics:
```json
{
  "system": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "memory": { "heapUsed": 47185920, "heapTotal": 70254592 },
    "cpu": { "user": 123456, "system": 78901 }
  },
  "application": {
    "requests": {
      "total": 1250,
      "successful": 1200,
      "failed": 50,
      "averageResponseTime": 145
    },
    "healthScore": 85,
    "status": "healthy"
  }
}
```

### Logging
Structured logging with different levels:
- **ERROR** - Errors and exceptions
- **WARN** - Warnings and degraded performance
- **INFO** - General information
- **DEBUG** - Detailed debugging information

## 🧪 Testing

The project includes comprehensive tests:

```bash
# Run all tests
npm test

# Run specific test file
npm test -- --testPathPatterns=server-setup.test.ts

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Categories
- **Unit Tests** - Individual functions and utilities
- **Integration Tests** - API endpoints and database operations
- **Health Check Tests** - Monitoring endpoints
- **Error Handling Tests** - Error scenarios

## 🚀 Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Use strong `JWT_SECRET` (32+ characters)
- [ ] Configure real Auth0 credentials
- [ ] Set up PostgreSQL database
- [ ] Configure `ALLOWED_ORIGINS` for your domains
- [ ] Set up Redis for caching (optional)
- [ ] Configure AWS S3 for file storage
- [ ] Set up monitoring and alerting
- [ ] Configure reverse proxy (nginx/Apache)
- [ ] Set up SSL/TLS certificates

### Docker Support
The server is designed to work well in containerized environments:
- Health check endpoints for container orchestration
- Graceful shutdown handling
- Environment-based configuration
- Structured logging for log aggregation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License.