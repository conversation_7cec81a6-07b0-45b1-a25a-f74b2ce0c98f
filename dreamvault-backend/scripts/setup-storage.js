#!/usr/bin/env node

/**
 * DreamVault Storage Setup Script
 * 
 * This script sets up Supabase storage buckets and policies for the DreamVault application.
 * It creates the necessary buckets, RLS policies, and helper functions for media file management.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('\nPlease check your .env file.')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function setupStorage() {
  console.log('🚀 Setting up DreamVault storage...\n')

  try {
    // Read the SQL setup file
    const sqlPath = path.join(__dirname, '../database/setup-storage.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')

    console.log('📄 Executing storage setup SQL...')
    
    // Execute the SQL setup
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent })
    
    if (error) {
      // If the RPC doesn't exist, try executing parts manually
      console.log('⚠️  RPC method not available, setting up manually...')
      await setupManually()
    } else {
      console.log('✅ Storage setup completed successfully!')
    }

    // Verify buckets were created
    console.log('\n🔍 Verifying storage buckets...')
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError.message)
      return
    }

    const requiredBuckets = ['media-files', 'avatars']
    const existingBuckets = buckets.map(b => b.id)
    
    for (const bucketId of requiredBuckets) {
      if (existingBuckets.includes(bucketId)) {
        console.log(`✅ Bucket '${bucketId}' exists`)
      } else {
        console.log(`❌ Bucket '${bucketId}' missing`)
      }
    }

    console.log('\n🎉 Storage setup verification complete!')
    console.log('\n📋 Next steps:')
    console.log('   1. Test file uploads from the mobile app')
    console.log('   2. Verify RLS policies are working correctly')
    console.log('   3. Check that files are organized in user folders')

  } catch (error) {
    console.error('❌ Error setting up storage:', error.message)
    process.exit(1)
  }
}

async function setupManually() {
  console.log('📦 Creating storage buckets manually...')

  // Create media-files bucket
  const { data: mediaData, error: mediaError } = await supabase.storage.createBucket('media-files', {
    public: true,
    fileSizeLimit: 52428800, // 50MB
    allowedMimeTypes: [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/quicktime',
      'audio/mpeg', 'audio/wav',
      'application/pdf'
    ]
  })

  if (mediaError && !mediaError.message.includes('already exists')) {
    console.error('❌ Error creating media-files bucket:', mediaError.message)
  } else {
    console.log('✅ Media-files bucket created/verified')
  }

  // Create avatars bucket
  const { data: avatarData, error: avatarError } = await supabase.storage.createBucket('avatars', {
    public: true,
    fileSizeLimit: 5242880, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  })

  if (avatarError && !avatarError.message.includes('already exists')) {
    console.error('❌ Error creating avatars bucket:', avatarError.message)
  } else {
    console.log('✅ Avatars bucket created/verified')
  }

  console.log('⚠️  Note: RLS policies need to be set up manually in the Supabase dashboard')
  console.log('   or by running the SQL commands from setup-storage.sql')
}

// Run the setup
setupStorage().catch(error => {
  console.error('❌ Setup failed:', error)
  process.exit(1)
})
