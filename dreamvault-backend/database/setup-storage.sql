-- DreamVault Storage Setup
-- This script sets up Supabase storage buckets and policies for media files

-- Create storage bucket for media files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media-files',
  'media-files',
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'audio/mpeg', 'audio/wav', 'application/pdf']
) ON CONFLICT (id) DO NOTHING;

-- Create storage bucket for user avatars
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars',
  'avatars',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for media-files bucket

-- Allow authenticated users to upload files to their own folder
CREATE POLICY "Users can upload media files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'media-files' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow authenticated users to view all public media files
CREATE POLICY "Users can view media files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
);

-- Allow users to update their own media files
CREATE POLICY "Users can update own media files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
) WITH CHECK (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to delete their own media files
CREATE POLICY "Users can delete own media files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Storage policies for avatars bucket

-- Allow authenticated users to upload avatars to their own folder
CREATE POLICY "Users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow authenticated users to view all avatars
CREATE POLICY "Users can view avatars" ON storage.objects
FOR SELECT USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
);

-- Allow users to update their own avatars
CREATE POLICY "Users can update own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
) WITH CHECK (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Allow users to delete their own avatars
CREATE POLICY "Users can delete own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create function to get file extension
CREATE OR REPLACE FUNCTION get_file_extension(filename text)
RETURNS text AS $$
BEGIN
  RETURN lower(substring(filename from '\.([^.]*)$'));
END;
$$ LANGUAGE plpgsql;

-- Create function to generate unique filename
CREATE OR REPLACE FUNCTION generate_unique_filename(user_id uuid, original_filename text)
RETURNS text AS $$
DECLARE
  file_ext text;
  unique_name text;
BEGIN
  file_ext := get_file_extension(original_filename);
  unique_name := user_id::text || '/' || extract(epoch from now())::bigint || '_' || gen_random_uuid()::text;
  
  IF file_ext IS NOT NULL THEN
    unique_name := unique_name || '.' || file_ext;
  END IF;
  
  RETURN unique_name;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up orphaned media files
CREATE OR REPLACE FUNCTION cleanup_orphaned_media_files()
RETURNS void AS $$
BEGIN
  -- Delete storage objects that don't have corresponding database records
  DELETE FROM storage.objects 
  WHERE bucket_id = 'media-files'
  AND name NOT IN (
    SELECT DISTINCT file_path 
    FROM media_files 
    WHERE file_path IS NOT NULL
  );
  
  -- Delete database records that don't have corresponding storage objects
  DELETE FROM media_files 
  WHERE file_path NOT IN (
    SELECT name 
    FROM storage.objects 
    WHERE bucket_id = 'media-files'
  );
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up orphaned files (run daily)
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-orphaned-media', '0 2 * * *', 'SELECT cleanup_orphaned_media_files();');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_name ON storage.objects(bucket_id, name);
CREATE INDEX IF NOT EXISTS idx_storage_objects_owner ON storage.objects(owner);

-- Add helpful comments
COMMENT ON FUNCTION get_file_extension(text) IS 'Extracts file extension from filename';
COMMENT ON FUNCTION generate_unique_filename(uuid, text) IS 'Generates unique filename with user folder structure';
COMMENT ON FUNCTION cleanup_orphaned_media_files() IS 'Removes orphaned media files from storage and database';
