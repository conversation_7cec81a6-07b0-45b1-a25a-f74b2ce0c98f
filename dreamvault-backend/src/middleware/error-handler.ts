import { Request, Response, NextFunction } from 'express'
import { ZodError } from 'zod'
import { config } from '../config'

// Custom error classes
export class AppError extends Error {
  public statusCode: number
  public code: string
  public isOperational: boolean
  public details?: any

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any
  ) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = true
    this.details = details

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details)
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR')
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND')
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT')
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED')
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(service: string) {
    super(`${service} is currently unavailable`, 503, 'SERVICE_UNAVAILABLE')
  }
}

// Error response interface
interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: any
    timestamp: string
    requestId: string
    stack?: string
  }
}

// Main error handling middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // If response already sent, delegate to default Express error handler
  if (res.headersSent) {
    return next(err)
  }

  const requestId = req.headers['x-request-id'] as string || 'unknown'
  const timestamp = new Date().toISOString()

  // Log error details
  console.error('Error occurred:', {
    error: err.message,
    stack: err.stack,
    requestId,
    url: req.url,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id
  })

  let statusCode = 500
  let code = 'INTERNAL_ERROR'
  let message = 'Internal server error'
  let details: any = undefined

  // Handle different error types
  if (err instanceof AppError) {
    statusCode = err.statusCode
    code = err.code
    message = err.message
    details = err.details
  } else if (err instanceof ZodError) {
    statusCode = 400
    code = 'VALIDATION_ERROR'
    message = 'Invalid request data'
    details = err.issues.map(issue => ({
      field: issue.path.join('.'),
      message: issue.message,
      code: issue.code
    }))
  } else if (err.name === 'JsonWebTokenError') {
    statusCode = 401
    code = 'INVALID_TOKEN'
    message = 'Invalid authentication token'
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401
    code = 'TOKEN_EXPIRED'
    message = 'Authentication token has expired'
  } else if (err.name === 'MulterError') {
    statusCode = 400
    code = 'FILE_UPLOAD_ERROR'
    message = 'File upload error'
    details = { type: (err as any).code, field: (err as any).field }
  } else if (err.message.includes('ECONNREFUSED')) {
    statusCode = 503
    code = 'DATABASE_CONNECTION_ERROR'
    message = 'Database connection failed'
  } else if (err.message.includes('ENOTFOUND')) {
    statusCode = 503
    code = 'EXTERNAL_SERVICE_ERROR'
    message = 'External service unavailable'
  }

  // Prepare error response
  const errorResponse: ErrorResponse = {
    error: {
      code,
      message,
      details,
      timestamp,
      requestId
    }
  }

  // Include stack trace in development
  if (config.server.isDevelopment && !(err instanceof AppError)) {
    errorResponse.error.stack = err.stack
  }

  // Send error response
  res.status(statusCode).json(errorResponse)
}

// 404 handler for unmatched routes
export const notFoundHandler = (req: Request, res: Response) => {
  const requestId = req.headers['x-request-id'] as string || 'unknown'
  
  res.status(404).json({
    error: {
      code: 'ROUTE_NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`,
      timestamp: new Date().toISOString(),
      requestId
    }
  })
}

// Async error wrapper for route handlers
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Success response helper
export const successResponse = (
  res: Response,
  data: any,
  message: string = 'Success',
  statusCode: number = 200
) => {
  res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  })
}

// Paginated response helper
export const paginatedResponse = (
  res: Response,
  data: any[],
  total: number,
  page: number,
  limit: number,
  message: string = 'Data retrieved successfully'
) => {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1

  res.json({
    success: true,
    message,
    data,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : null,
      prevPage: hasPrev ? page - 1 : null
    },
    timestamp: new Date().toISOString()
  })
}

// Health check response helper
export const healthResponse = (
  res: Response,
  checks: Record<string, { status: 'healthy' | 'unhealthy', details?: any }>
) => {
  const allHealthy = Object.values(checks).every(check => check.status === 'healthy')
  const statusCode = allHealthy ? 200 : 503

  res.status(statusCode).json({
    status: allHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.server.nodeEnv,
    checks
  })
}
