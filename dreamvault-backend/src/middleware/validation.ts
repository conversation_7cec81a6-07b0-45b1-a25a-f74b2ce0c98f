import { Request, Response, NextFunction } from 'express'
import { z, ZodSchema, ZodError } from 'zod'
import rateLimitLib, { ipKeyGenerator } from 'express-rate-limit'
import compression from 'compression'
import { config } from '../config'

// Enhanced request validation middleware
export function validateRequest(schema: {
  body?: ZodSchema
  query?: ZodSchema
  params?: ZodSchema
}) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate request body
      if (schema.body) {
        const bodyValidation = schema.body.safeParse(req.body)
        if (!bodyValidation.success) {
          res.status(400).json({
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Invalid request body',
              details: formatZodErrors(bodyValidation.error),
              timestamp: new Date().toISOString(),
              requestId: req.headers['x-request-id'] || 'unknown'
            }
          })
          return
        }
        req.body = bodyValidation.data
      }

      // Validate query parameters
      if (schema.query) {
        const queryValidation = schema.query.safeParse(req.query)
        if (!queryValidation.success) {
          res.status(400).json({
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Invalid query parameters',
              details: formatZodErrors(queryValidation.error),
              timestamp: new Date().toISOString(),
              requestId: req.headers['x-request-id'] || 'unknown'
            }
          })
          return
        }
        req.query = queryValidation.data
      }

      // Validate route parameters
      if (schema.params) {
        const paramsValidation = schema.params.safeParse(req.params)
        if (!paramsValidation.success) {
          res.status(400).json({
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Invalid route parameters',
              details: formatZodErrors(paramsValidation.error),
              timestamp: new Date().toISOString(),
              requestId: req.headers['x-request-id'] || 'unknown'
            }
          })
          return
        }
        req.params = paramsValidation.data
      }

      next()
    } catch (error) {
      console.error('Validation middleware error:', error)
      res.status(500).json({
        error: {
          code: 'VALIDATION_MIDDLEWARE_ERROR',
          message: 'Internal validation error',
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      })
      return
    }
  }
}

// Format Zod errors for better readability
function formatZodErrors(error: ZodError) {
  return error.issues.map(issue => ({
    field: issue.path.join('.'),
    message: issue.message,
    code: issue.code,
    received: 'received' in issue ? issue.received : undefined
  }))
}

// Enhanced rate limiting with different tiers
export const createRateLimit = (options: {
  windowMs?: number
  max?: number
  message?: string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}) => {
  return rateLimitLib({
    windowMs: options.windowMs || config.security.rateLimitWindowMs,
    max: options.max || config.security.rateLimitMax,
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: options.message || 'Too many requests, please try again later',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    skipFailedRequests: options.skipFailedRequests || false,
    // Use default keyGenerator for now to avoid IPv6 issues
    // keyGenerator: (req) => (req as any).user?.id || req.ip || 'unknown'
  })
}

// Different rate limit configurations
export const rateLimits = {
  // General API rate limit
  general: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // 1000 requests per window
    message: 'Too many requests from this IP, please try again later'
  }),

  // Strict rate limit for sensitive operations
  strict: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
    message: 'Rate limit exceeded for sensitive operations'
  }),

  // AI service rate limit (relaxed for development)
  ai: createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: process.env.NODE_ENV === 'development' ? 200 : 20, // 200 in dev, 20 in prod
    message: 'AI service rate limit exceeded, please wait before making more requests'
  }),

  // Media upload rate limit
  upload: createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 uploads per minute
    message: 'Upload rate limit exceeded, please wait before uploading more files'
  }),

  // Authentication rate limit
  auth: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 auth attempts per window
    message: 'Too many authentication attempts, please try again later',
    skipSuccessfulRequests: true
  })
}

// Request compression middleware
export const compressionMiddleware = compression({
  filter: (req: Request, res: Response) => {
    if (req.headers['x-no-compression']) {
      return false
    }
    return compression.filter(req, res)
  },
  threshold: 1024, // Only compress responses larger than 1KB
  level: 6 // Compression level (1-9, 6 is good balance)
})

// Request timeout middleware
export const timeout = (ms: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const timer = setTimeout(() => {
      if (!res.headersSent) {
        res.status(408).json({
          error: {
            code: 'REQUEST_TIMEOUT',
            message: `Request timeout after ${ms}ms`,
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] || 'unknown'
          }
        })
      }
    }, ms)

    res.on('finish', () => {
      clearTimeout(timer)
    })

    res.on('close', () => {
      clearTimeout(timer)
    })

    next()
  }
}

// Common validation schemas
export const commonSchemas = {
  // Pagination schema
  pagination: z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    limit: z.string().optional().transform(val => val ? Math.min(parseInt(val, 10), 100) : 20),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional().default('desc')
  }),

  // UUID parameter schema
  uuidParam: z.object({
    id: z.string().uuid('Invalid UUID format')
  }),

  // Search query schema
  search: z.object({
    q: z.string().min(1).max(100),
    category: z.string().optional(),
    status: z.string().optional()
  })
}

// Sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Basic XSS protection - remove script tags and dangerous attributes
  const sanitizeString = (str: string): string => {
    return str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
  }

  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return sanitizeString(obj)
    }
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject)
    }
    if (obj && typeof obj === 'object') {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value)
      }
      return sanitized
    }
    return obj
  }

  if (req.body) {
    req.body = sanitizeObject(req.body)
  }
  if (req.query) {
    req.query = sanitizeObject(req.query)
  }

  next()
}
