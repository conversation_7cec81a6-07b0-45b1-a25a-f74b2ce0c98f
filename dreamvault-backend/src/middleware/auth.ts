import { Request, Response, NextFunction } from 'express'
import { supabase } from '../lib/supabase'
import { config } from '../config'

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        email: string
        user_metadata?: any
      }
    }
  }
}

export const authenticateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Development mode bypass - allow testing without authentication
    if (config.server.isDevelopment && process.env.SKIP_AUTH === 'true') {
      console.log('🔓 Development mode: Skipping authentication')
      req.user = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        user_metadata: { name: 'Development User' }
      }
      next()
      return
    }

    const authHeader = req.headers.authorization

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No authorization token provided' })
      return
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify the JWT token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      console.error('Supabase auth error:', error)
      res.status(401).json({ error: 'Invalid or expired token' })
      return
    }

    // Add user to request object
    req.user = {
      id: user.id,
      email: user.email!,
      user_metadata: user.user_metadata
    }

    next()
  } catch (error) {
    console.error('Authentication error:', error)
    res.status(401).json({ error: 'Authentication failed' })
    return
  }
}

// Export alias for consistency with imports
export const authenticate = authenticateUser
