import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { requestId, rateLimit, sanitizeBody } from './lib/auth'
import { config } from './config'
import { sentryRequestHandler, sentryErrorHandler } from './lib/sentry'
import { compressionMiddleware, timeout, sanitizeInput, rateLimits } from './middleware/validation'
import { errorHandler, notFoundHandler } from './middleware/error-handler'
import apiRoutes from './routes'

// Create Express app
export const app = express()

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1)

// Sentry request handler (must be first)
app.use(sentryRequestHandler)

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}))

// CORS configuration
app.use(cors({
  origin: config.server.allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// Compression middleware
app.use(compressionMiddleware)

// Request timeout middleware
app.use(timeout(30000)) // 30 seconds

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Request middleware
app.use(requestId)
app.use(rateLimits.general)
app.use(sanitizeInput)
app.use(sanitizeBody)

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: 'connected'
    }
    
    res.status(200).json(health)
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// API routes
app.use('/api', apiRoutes)

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'DreamVault API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      api: '/api',
      docs: '/api/docs'
    }
  })
})

// 404 handler
app.use('*', notFoundHandler)

// Sentry error handler (must be before custom error handler)
app.use(sentryErrorHandler)

// Global error handler (must be last)
app.use(errorHandler)

export default app
