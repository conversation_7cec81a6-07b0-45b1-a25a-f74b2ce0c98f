// Utility functions for generating visual progress indicators and analytics

export interface ProgressIndicator {
  percentage: number
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold'
  color: string
  label: string
  icon: string
}

export interface ProgressRing {
  percentage: number
  strokeColor: string
  backgroundColor: string
  size: 'small' | 'medium' | 'large'
  showPercentage: boolean
}

export interface ProgressBar {
  percentage: number
  height: number
  backgroundColor: string
  fillColor: string
  animated: boolean
  showLabel: boolean
}

// Generate progress indicator configuration
export function generateProgressIndicator(completionPercentage: number, status: string): ProgressIndicator {
  let color: string
  let icon: string
  let label: string

  switch (status) {
    case 'COMPLETED':
      color = '#10B981' // Green
      icon = '✅'
      label = 'Completed'
      break
    case 'IN_PROGRESS':
      if (completionPercentage >= 75) {
        color = '#F59E0B' // Amber
        icon = '🔥'
        label = 'Almost there!'
      } else if (completionPercentage >= 50) {
        color = '#3B82F6' // Blue
        icon = '⚡'
        label = 'Making progress'
      } else {
        color = '#6366F1' // Indigo
        icon = '🚀'
        label = 'Getting started'
      }
      break
    case 'ON_HOLD':
      color = '#6B7280' // Gray
      icon = '⏸️'
      label = 'On hold'
      break
    default: // NOT_STARTED
      color = '#E5E7EB' // Light gray
      icon = '⭕'
      label = 'Not started'
      break
  }

  return {
    percentage: completionPercentage,
    status: status.toLowerCase().replace('_', '-') as any,
    color,
    label,
    icon
  }
}

// Generate progress ring configuration
export function generateProgressRing(
  completionPercentage: number, 
  size: 'small' | 'medium' | 'large' = 'medium',
  showPercentage: boolean = true
): ProgressRing {
  let strokeColor: string
  let backgroundColor = '#F3F4F6'

  if (completionPercentage === 100) {
    strokeColor = '#10B981' // Green
  } else if (completionPercentage >= 75) {
    strokeColor = '#F59E0B' // Amber
  } else if (completionPercentage >= 50) {
    strokeColor = '#3B82F6' // Blue
  } else if (completionPercentage > 0) {
    strokeColor = '#6366F1' // Indigo
  } else {
    strokeColor = '#E5E7EB' // Light gray
  }

  return {
    percentage: completionPercentage,
    strokeColor,
    backgroundColor,
    size,
    showPercentage
  }
}

// Generate progress bar configuration
export function generateProgressBar(
  completionPercentage: number,
  height: number = 8,
  animated: boolean = true,
  showLabel: boolean = false
): ProgressBar {
  let fillColor: string
  const backgroundColor = '#F3F4F6'

  if (completionPercentage === 100) {
    fillColor = '#10B981' // Green
  } else if (completionPercentage >= 75) {
    fillColor = '#3B82F6' // Blue
  } else if (completionPercentage >= 50) {
    fillColor = '#F59E0B' // Yellow
  } else {
    fillColor = '#EF4444' // Red
  }

  return {
    percentage: completionPercentage,
    height: 20,
    backgroundColor,
    fillColor,
    animated,
    showLabel
  }
}