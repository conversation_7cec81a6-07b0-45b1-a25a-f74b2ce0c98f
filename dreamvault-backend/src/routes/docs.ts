import { Router, Request, Response } from 'express'
import { config } from '../config'

const router = Router()

// API Documentation endpoint
router.get('/', (req: Request, res: Response) => {
  const apiDocs = {
    name: 'DreamVault API',
    version: '1.0.0',
    description: 'Backend API for the DreamVault bucket list application',
    baseUrl: `http://localhost:${config.server.port}/api`,
    environment: config.server.nodeEnv,
    
    authentication: {
      type: 'Bearer Token',
      description: 'Include Supabase JWT token in Authorization header',
      header: 'Authorization: Bearer <token>'
    },

    endpoints: {
      health: {
        '/health': {
          method: 'GET',
          description: 'Basic health check',
          authentication: false,
          response: {
            status: 'healthy | unhealthy',
            timestamp: 'ISO string',
            uptime: 'number (seconds)',
            checks: 'object'
          }
        },
        '/health/detailed': {
          method: 'GET',
          description: 'Detailed health check with all services',
          authentication: false
        },
        '/health/database': {
          method: 'GET',
          description: 'Database connectivity check',
          authentication: false
        },
        '/health/cache': {
          method: 'GET',
          description: 'Cache service check',
          authentication: false
        },
        '/health/ai': {
          method: 'GET',
          description: 'AI services availability check',
          authentication: false
        },
        '/health/storage': {
          method: 'GET',
          description: 'Storage service check',
          authentication: false
        },
        '/health/ready': {
          method: 'GET',
          description: 'Kubernetes readiness probe',
          authentication: false
        },
        '/health/live': {
          method: 'GET',
          description: 'Kubernetes liveness probe',
          authentication: false
        }
      },

      ai: {
        '/ai/suggest-categories': {
          method: 'POST',
          description: 'Get AI-powered category suggestions for a goal',
          authentication: true,
          body: {
            title: 'string (required)',
            description: 'string (optional)'
          },
          response: {
            categories: 'string[]',
            confidence: 'number'
          }
        },
        '/ai/extract-locations': {
          method: 'POST',
          description: 'Extract location information from goal text',
          authentication: true,
          body: {
            title: 'string (required)',
            description: 'string (optional)'
          },
          response: {
            locations: 'object[]',
            confidence: 'number'
          }
        },
        '/ai/suggest-tags': {
          method: 'POST',
          description: 'Generate relevant tags for a goal',
          authentication: true,
          body: {
            title: 'string (required)',
            description: 'string (optional)'
          },
          response: {
            tags: 'string[]',
            confidence: 'number'
          }
        },
        '/ai/moderate-content': {
          method: 'POST',
          description: 'Check content for appropriateness',
          authentication: true,
          body: {
            content: 'string (required)'
          },
          response: {
            isAppropriate: 'boolean',
            confidence: 'number',
            reasons: 'string[]'
          }
        },
        '/ai/smart-suggestions': {
          method: 'POST',
          description: 'Get comprehensive AI suggestions for a goal',
          authentication: true,
          body: {
            title: 'string (required)',
            description: 'string (optional)'
          },
          response: {
            categories: 'string[]',
            tags: 'string[]',
            locations: 'object',
            estimatedDuration: 'string',
            estimatedCost: 'string',
            bestTimeToVisit: 'string'
          }
        },
        '/ai/status': {
          method: 'GET',
          description: 'Check AI service availability and features',
          authentication: true,
          response: {
            isAvailable: 'boolean',
            service: 'string',
            features: 'string[]'
          }
        }
      }
    },

    errorCodes: {
      'VALIDATION_ERROR': {
        status: 400,
        description: 'Request validation failed'
      },
      'AUTHENTICATION_ERROR': {
        status: 401,
        description: 'Authentication required or invalid token'
      },
      'AUTHORIZATION_ERROR': {
        status: 403,
        description: 'Insufficient permissions'
      },
      'NOT_FOUND': {
        status: 404,
        description: 'Resource not found'
      },
      'CONFLICT': {
        status: 409,
        description: 'Resource conflict'
      },
      'RATE_LIMIT_EXCEEDED': {
        status: 429,
        description: 'Too many requests'
      },
      'INTERNAL_ERROR': {
        status: 500,
        description: 'Internal server error'
      },
      'SERVICE_UNAVAILABLE': {
        status: 503,
        description: 'Service temporarily unavailable'
      }
    },

    rateLimits: {
      general: '1000 requests per 15 minutes',
      strict: '100 requests per 15 minutes',
      ai: '20 requests per minute',
      upload: '10 uploads per minute',
      auth: '5 attempts per 15 minutes'
    },

    responseFormat: {
      success: {
        success: true,
        message: 'string',
        data: 'any',
        timestamp: 'ISO string'
      },
      error: {
        error: {
          code: 'string',
          message: 'string',
          details: 'any (optional)',
          timestamp: 'ISO string',
          requestId: 'string'
        }
      },
      paginated: {
        success: true,
        message: 'string',
        data: 'array',
        pagination: {
          total: 'number',
          page: 'number',
          limit: 'number',
          totalPages: 'number',
          hasNext: 'boolean',
          hasPrev: 'boolean',
          nextPage: 'number | null',
          prevPage: 'number | null'
        },
        timestamp: 'ISO string'
      }
    },

    examples: {
      aiCategorySuggestion: {
        request: {
          method: 'POST',
          url: '/api/ai/suggest-categories',
          headers: {
            'Authorization': 'Bearer <your-token>',
            'Content-Type': 'application/json'
          },
          body: {
            title: 'Learn to play guitar',
            description: 'I want to learn acoustic guitar and play my favorite songs'
          }
        },
        response: {
          message: 'Categories suggested successfully',
          categories: ['Learning', 'Music', 'Personal Development'],
          confidence: 0.95
        }
      },
      healthCheck: {
        request: {
          method: 'GET',
          url: '/health'
        },
        response: {
          status: 'healthy',
          timestamp: '2024-01-15T10:30:00.000Z',
          uptime: 3600,
          environment: 'development',
          checks: {
            api: { status: 'healthy' },
            database: { status: 'healthy', details: { responseTime: 45 } },
            cache: { status: 'healthy', details: { responseTime: 12 } }
          }
        }
      }
    },

    support: {
      documentation: 'https://github.com/your-org/dreamvault/wiki',
      issues: 'https://github.com/your-org/dreamvault/issues',
      contact: '<EMAIL>'
    }
  }

  res.json(apiDocs)
})

// OpenAPI/Swagger specification
router.get('/openapi', (req: Request, res: Response) => {
  const openApiSpec = {
    openapi: '3.0.0',
    info: {
      title: 'DreamVault API',
      version: '1.0.0',
      description: 'Backend API for the DreamVault bucket list application',
      contact: {
        name: 'DreamVault Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `http://localhost:${config.server.port}/api`,
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    paths: {
      '/health': {
        get: {
          summary: 'Health check',
          description: 'Check the health status of the API and its dependencies',
          security: [],
          responses: {
            '200': {
              description: 'Service is healthy',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      status: { type: 'string', enum: ['healthy', 'unhealthy'] },
                      timestamp: { type: 'string', format: 'date-time' },
                      uptime: { type: 'number' },
                      checks: { type: 'object' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  res.json(openApiSpec)
})

export default router
