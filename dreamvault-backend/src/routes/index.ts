import { Router } from 'express'
import aiRoutes from './ai'
import healthRoutes from './health'
import docsRoutes from './docs'
import { rateLimits } from '../middleware/validation'

const router = Router()

// Mount route modules
router.use('/health', healthRoutes)
router.use('/docs', docsRoutes)
router.use('/ai', rateLimits.ai, aiRoutes)

// API root endpoint
router.get('/', (_req, res) => {
  res.json({
    name: 'DreamVault API',
    version: '1.0.0',
    description: 'Backend API for the DreamVault bucket list application',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      ai: '/api/ai',
      docs: '/api/docs'
    },
    features: [
      'AI-powered suggestions',
      'Health monitoring',
      'Comprehensive error handling',
      'Rate limiting',
      'Request validation',
      'Caching support'
    ]
  })
})

export default router