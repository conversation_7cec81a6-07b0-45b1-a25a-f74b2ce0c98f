import { Router, Request, Response } from 'express'
import { supabase } from '../lib/supabase'
import { cache } from '../lib/cache'
import { config } from '../config'
import { healthResponse, asyncHandler } from '../middleware/error-handler'

const router = Router()

// Basic health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    api: { status: 'healthy' as const },
    database: await checkDatabase(),
    cache: await checkCache(),
    memory: checkMemory(),
    disk: checkDisk()
  }

  healthResponse(res, checks)
}))

// Detailed health check
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    api: { status: 'healthy' as const },
    database: await checkDatabase(),
    cache: await checkCache(),
    memory: checkMemory(),
    disk: checkDisk(),
    externalServices: await checkExternalServices(),
    environment: checkEnvironment()
  }

  healthResponse(res, checks)
}))

// Database-specific health check
router.get('/database', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    database: await checkDatabase()
  }

  healthResponse(res, checks)
}))

// Cache-specific health check
router.get('/cache', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    cache: await checkCache()
  }

  healthResponse(res, checks)
}))

// AI services health check
router.get('/ai', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    ai: await checkAIServices()
  }

  healthResponse(res, checks)
}))

// Storage health check
router.get('/storage', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    storage: await checkStorage()
  }

  healthResponse(res, checks)
}))

// Readiness probe (for Kubernetes)
router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    database: await checkDatabase(),
    cache: await checkCache()
  }

  const isReady = Object.values(checks).every(check => check.status === 'healthy')
  
  if (isReady) {
    res.status(200).json({ status: 'ready', timestamp: new Date().toISOString() })
  } else {
    res.status(503).json({ status: 'not ready', checks, timestamp: new Date().toISOString() })
  }
}))

// Liveness probe (for Kubernetes)
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({ 
    status: 'alive', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// Health check functions
async function checkDatabase() {
  try {
    const startTime = Date.now()
    const { data, error } = await supabase.from('users').select('id').limit(1)
    const responseTime = Date.now() - startTime

    if (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: error.message, responseTime }
      }
    }

    return {
      status: 'healthy' as const,
      details: { responseTime, recordCount: data?.length || 0 }
    }
  } catch (error) {
    return {
      status: 'unhealthy' as const,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkCache() {
  if (!cache.isAvailable()) {
    return {
      status: 'unhealthy' as const,
      details: { error: 'Cache service not available' }
    }
  }

  try {
    const testKey = 'health-check'
    const testValue = { timestamp: Date.now() }
    
    const startTime = Date.now()
    await cache.set(testKey, testValue, { ttl: 60 })
    const retrieved = await cache.get(testKey)
    await cache.delete(testKey)
    const responseTime = Date.now() - startTime

    if (JSON.stringify(retrieved) !== JSON.stringify(testValue)) {
      return {
        status: 'unhealthy' as const,
        details: { error: 'Cache read/write test failed', responseTime }
      }
    }

    const stats = cache.getStats()
    return {
      status: 'healthy' as const,
      details: { responseTime, stats }
    }
  } catch (error) {
    return {
      status: 'unhealthy' as const,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

function checkMemory() {
  const usage = process.memoryUsage()
  const totalMB = Math.round(usage.heapTotal / 1024 / 1024)
  const usedMB = Math.round(usage.heapUsed / 1024 / 1024)
  const usagePercent = Math.round((usage.heapUsed / usage.heapTotal) * 100)

  // Consider unhealthy if memory usage is above 90%
  const isHealthy = usagePercent < 90

  return {
    status: isHealthy ? 'healthy' as const : 'unhealthy' as const,
    details: {
      totalMB,
      usedMB,
      usagePercent,
      rss: Math.round(usage.rss / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024)
    }
  }
}

function checkDisk() {
  // Basic disk space check (simplified)
  try {
    const fs = require('fs')
    const stats = fs.statSync('.')
    
    return {
      status: 'healthy' as const,
      details: { available: true }
    }
  } catch (error) {
    return {
      status: 'unhealthy' as const,
      details: { error: 'Disk access failed' }
    }
  }
}

async function checkExternalServices() {
  const services = {
    openai: config.externalApis.openaiApiKey ? 'configured' : 'not configured',
    google: config.externalApis.googleApiKey ? 'configured' : 'not configured'
  }

  return {
    status: 'healthy' as const,
    details: services
  }
}

function checkEnvironment() {
  const requiredEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'SUPABASE_ANON_KEY'
  ]

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])
  const isHealthy = missing.length === 0

  return {
    status: isHealthy ? 'healthy' as const : 'unhealthy' as const,
    details: {
      nodeEnv: config.server.nodeEnv,
      nodeVersion: process.version,
      platform: process.platform,
      missingEnvVars: missing
    }
  }
}

async function checkAIServices() {
  try {
    const hasOpenAI = !!config.externalApis.openaiApiKey
    const hasGoogle = !!config.externalApis.googleApiKey

    return {
      status: (hasOpenAI || hasGoogle) ? 'healthy' as const : 'unhealthy' as const,
      details: {
        openai: hasOpenAI ? 'available' : 'not configured',
        google: hasGoogle ? 'available' : 'not configured'
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy' as const,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkStorage() {
  try {
    // Check if Supabase storage is accessible
    const { data, error } = await supabase.storage.listBuckets()

    if (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: error.message }
      }
    }

    return {
      status: 'healthy' as const,
      details: { buckets: data?.length || 0 }
    }
  } catch (error) {
    return {
      status: 'unhealthy' as const,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

export default router
