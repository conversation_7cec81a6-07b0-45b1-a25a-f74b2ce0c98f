import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { v4 as uuidv4 } from 'uuid'
import { config } from '../config'
import { Logger } from './monitoring'
import { captureException } from './sentry'

export interface UploadResult {
  key: string
  url: string
  bucket: string
  size: number
  contentType: string
}

export interface PresignedUrlOptions {
  expiresIn?: number
  contentType?: string
  contentLength?: number
}

class S3Service {
  private client: S3Client
  private bucket: string

  constructor() {
    if (!config.aws.accessKeyId || !config.aws.secretAccessKey) {
      Logger.warn('AWS credentials not configured, S3 operations will fail')
    }

    this.client = new S3Client({
      region: config.aws.region,
      credentials: config.aws.accessKeyId && config.aws.secretAccessKey ? {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey
      } : undefined
    })
    
    this.bucket = config.aws.s3Bucket
  }

  /**
   * Upload a file buffer to S3
   */
  async uploadFile(
    buffer: Buffer,
    contentType: string,
    folder: string = 'uploads',
    filename?: string
  ): Promise<UploadResult> {
    try {
      const key = this.generateKey(folder, filename, contentType)
      
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        ServerSideEncryption: 'AES256',
        Metadata: {
          uploadedAt: new Date().toISOString(),
          originalSize: buffer.length.toString()
        }
      })

      await this.client.send(command)
      
      const url = `https://${this.bucket}.s3.${config.aws.region}.amazonaws.com/${key}`
      
      Logger.info('File uploaded to S3', {
        key,
        bucket: this.bucket,
        size: buffer.length,
        contentType
      })

      return {
        key,
        url,
        bucket: this.bucket,
        size: buffer.length,
        contentType
      }
    } catch (error) {
      Logger.error('Failed to upload file to S3', error)
      captureException(error as Error, {
        extra: { folder, filename, contentType, bufferSize: buffer.length }
      })
      throw new Error('File upload failed')
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key
      })

      await this.client.send(command)
      
      Logger.info('File deleted from S3', { key, bucket: this.bucket })
    } catch (error) {
      Logger.error('Failed to delete file from S3', error)
      captureException(error as Error, {
        extra: { key, bucket: this.bucket }
      })
      throw new Error('File deletion failed')
    }
  }

  /**
   * Generate a presigned URL for direct upload
   */
  async getPresignedUploadUrl(
    folder: string,
    contentType: string,
    options: PresignedUrlOptions = {}
  ): Promise<{ url: string; key: string; fields: Record<string, string> }> {
    try {
      const key = this.generateKey(folder, undefined, contentType)
      const expiresIn = options.expiresIn || 3600 // 1 hour default

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        ContentType: contentType,
        ServerSideEncryption: 'AES256',
        ...(options.contentLength && { ContentLength: options.contentLength })
      })

      const url = await getSignedUrl(this.client, command, { expiresIn })

      return {
        url,
        key,
        fields: {
          'Content-Type': contentType,
          'x-amz-server-side-encryption': 'AES256'
        }
      }
    } catch (error) {
      Logger.error('Failed to generate presigned upload URL', error)
      captureException(error as Error, {
        extra: { folder, contentType, options }
      })
      throw new Error('Failed to generate upload URL')
    }
  }

  /**
   * Generate a presigned URL for download
   */
  async getPresignedDownloadUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key
      })

      const url = await getSignedUrl(this.client, command, { expiresIn })
      return url
    } catch (error) {
      Logger.error('Failed to generate presigned download URL', error)
      captureException(error as Error, {
        extra: { key, expiresIn }
      })
      throw new Error('Failed to generate download URL')
    }
  }

  /**
   * Check if a file exists in S3
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key
      })

      await this.client.send(command)
      return true
    } catch (error: any) {
      if (error.name === 'NotFound') {
        return false
      }
      throw error
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(key: string): Promise<{
    size: number
    contentType: string
    lastModified: Date
    metadata: Record<string, string>
  }> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key
      })

      const response = await this.client.send(command)

      return {
        size: response.ContentLength || 0,
        contentType: response.ContentType || 'application/octet-stream',
        lastModified: response.LastModified || new Date(),
        metadata: response.Metadata || {}
      }
    } catch (error) {
      Logger.error('Failed to get file metadata', error)
      captureException(error as Error, {
        extra: { key }
      })
      throw new Error('Failed to get file metadata')
    }
  }

  /**
   * Generate a unique S3 key
   */
  private generateKey(folder: string, filename?: string, contentType?: string): string {
    const timestamp = Date.now()
    const uuid = uuidv4()
    
    let extension = ''
    if (contentType) {
      const mimeToExt: Record<string, string> = {
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'video/mp4': '.mp4',
        'video/quicktime': '.mov',
        'video/webm': '.webm',
        'audio/mpeg': '.mp3',
        'audio/wav': '.wav',
        'audio/webm': '.webm',
        'audio/mp4': '.m4a'
      }
      extension = mimeToExt[contentType] || ''
    }

    if (filename) {
      const cleanFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
      return `${folder}/${timestamp}-${uuid}-${cleanFilename}`
    }

    return `${folder}/${timestamp}-${uuid}${extension}`
  }

  /**
   * Extract key from S3 URL
   */
  extractKeyFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url)
      
      // Handle different S3 URL formats
      if (urlObj.hostname.includes('.s3.') || urlObj.hostname.includes('.s3-')) {
        return urlObj.pathname.substring(1) // Remove leading slash
      }
      
      // Handle s3://bucket/key format
      if (urlObj.protocol === 's3:') {
        return urlObj.pathname.substring(1)
      }
      
      return null
    } catch {
      return null
    }
  }

  /**
   * Static version of extractKeyFromUrl for backward compatibility
   */
  static extractKeyFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url)

      // Handle different S3 URL formats
      if (urlObj.hostname.includes('.s3.') || urlObj.hostname.includes('.s3-')) {
        return urlObj.pathname.substring(1) // Remove leading slash
      }

      // Handle s3://bucket/key format
      if (urlObj.protocol === 's3:') {
        return urlObj.pathname.substring(1)
      }

      return null
    } catch {
      return null
    }
  }
}

// Singleton instance
export const s3Service = new S3Service()
export default s3Service
