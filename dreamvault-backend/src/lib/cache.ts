import Redis from 'ioredis'
import { config } from '../config'

interface CacheOptions {
  ttl?: number // Time to live in seconds
  prefix?: string
}

interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
}

class CacheService {
  private redis: Redis | null = null
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0
  }
  private isEnabled: boolean = false

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      if (config.cache?.redisUrl && config.server.nodeEnv === 'production') {
        this.redis = new Redis(config.cache.redisUrl as string, {
          maxRetriesPerRequest: 3,
          lazyConnect: true,
          connectTimeout: 10000,
          commandTimeout: 5000,
        })

        this.redis.on('connect', () => {
          console.log('✅ Redis cache connected')
          this.isEnabled = true
        })

        this.redis.on('error', (error) => {
          console.error('❌ Redis cache error:', error.message)
          this.isEnabled = false
        })

        this.redis.on('close', () => {
          console.log('🔌 Redis cache disconnected')
          this.isEnabled = false
        })

        // Test connection
        await this.redis.ping()
      } else {
        console.log('ℹ️  Redis cache disabled in development mode')
      }
    } catch (error) {
      console.error('❌ Failed to initialize Redis cache:', error)
      this.isEnabled = false
    }
  }

  /**
   * Get value from cache
   */
  async get<T = any>(key: string, options: CacheOptions = {}): Promise<T | null> {
    if (!this.isEnabled || !this.redis) {
      this.stats.misses++
      return null
    }

    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix)
      const value = await this.redis.get(prefixedKey)
      
      if (value === null) {
        this.stats.misses++
        return null
      }

      this.stats.hits++
      return JSON.parse(value)
    } catch (error) {
      console.error('Cache get error:', error)
      this.stats.misses++
      return null
    }
  }

  /**
   * Set value in cache
   */
  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    if (!this.isEnabled || !this.redis) {
      return false
    }

    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix)
      const serializedValue = JSON.stringify(value)
      const ttl = options.ttl || config.cache?.defaultTtl || 3600 // 1 hour default

      await this.redis.setex(prefixedKey, ttl, serializedValue)
      this.stats.sets++
      return true
    } catch (error) {
      console.error('Cache set error:', error)
      return false
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.isEnabled || !this.redis) {
      return false
    }

    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix)
      const result = await this.redis.del(prefixedKey)
      this.stats.deletes++
      return result > 0
    } catch (error) {
      console.error('Cache delete error:', error)
      return false
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  async deletePattern(pattern: string, options: CacheOptions = {}): Promise<number> {
    if (!this.isEnabled || !this.redis) {
      return 0
    }

    try {
      const prefixedPattern = this.getPrefixedKey(pattern, options.prefix)
      const keys = await this.redis.keys(prefixedPattern)
      
      if (keys.length === 0) {
        return 0
      }

      const result = await this.redis.del(...keys)
      this.stats.deletes += result
      return result
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      return 0
    }
  }

  /**
   * Check if cache is available
   */
  isAvailable(): boolean {
    return this.isEnabled && this.redis !== null
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats & { hitRate: number; isEnabled: boolean } {
    const total = this.stats.hits + this.stats.misses
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0

    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      isEnabled: this.isEnabled
    }
  }

  /**
   * Clear all cache statistics
   */
  clearStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    }
  }

  /**
   * Flush all cache data
   */
  async flush(): Promise<boolean> {
    if (!this.isEnabled || !this.redis) {
      return false
    }

    try {
      await this.redis.flushdb()
      return true
    } catch (error) {
      console.error('Cache flush error:', error)
      return false
    }
  }

  /**
   * Close cache connection
   */
  async close(): Promise<void> {
    if (this.redis) {
      await this.redis.quit()
      this.redis = null
      this.isEnabled = false
    }
  }

  private getPrefixedKey(key: string, prefix?: string): string {
    const basePrefix = config.cache?.keyPrefix || 'dreamvault'
    const fullPrefix = prefix ? `${basePrefix}:${prefix}` : basePrefix
    return `${fullPrefix}:${key}`
  }
}

// Singleton instance
export const cache = new CacheService()

/**
 * Cache decorator for functions
 */
export function cached(ttl: number = 3600, prefix?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${propertyName}:${JSON.stringify(args)}`
      
      // Try to get from cache first
      const cachedResult = await cache.get(cacheKey, { prefix })
      if (cachedResult !== null) {
        return cachedResult
      }

      // Execute original method
      const result = await method.apply(this, args)
      
      // Cache the result
      await cache.set(cacheKey, result, { ttl, prefix })
      
      return result
    }
  }
}

export default cache
