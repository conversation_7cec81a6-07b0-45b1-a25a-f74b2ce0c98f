// Simplified socket service stub for Supabase migration
import { Logger } from './monitoring'

export interface SocketEventData {
  goalCreated: { goalId: string; userId: string }
  goalUpdated: { goalId: string; userId: string }
  goalCompleted: { goalId: string; userId: string }
  progressUpdated: { goalId: string; userId: string; progress: number }
}

class SocketService {
  private isInitialized = false

  async initialize(): Promise<void> {
    Logger.info('Socket service initialized (stub)')
    this.isInitialized = true
  }

  async emitToUser<K extends keyof SocketEventData>(
    userId: string,
    event: K,
    data: SocketEventData[K]
  ): Promise<void> {
    Logger.info('Socket event emitted to user (stub)', { userId, event, data })
  }

  async emitToRoom(room: string, event: string, data: any): Promise<void> {
    Logger.info('Socket event emitted to room (stub)', { room, event, data })
  }

  async joinRoom(socketId: string, room: string): Promise<void> {
    Logger.info('Socket joined room (stub)', { socketId, room })
  }

  async leaveRoom(socketId: string, room: string): Promise<void> {
    Logger.info('Socket left room (stub)', { socketId, room })
  }

  getConnectedUsers(): string[] {
    return []
  }

  isUserConnected(userId: string): boolean {
    return false
  }

  async shutdown(): Promise<void> {
    Logger.info('Socket service shutdown (stub)')
    this.isInitialized = false
  }
}

export const socketService = new SocketService()
export default socketService
