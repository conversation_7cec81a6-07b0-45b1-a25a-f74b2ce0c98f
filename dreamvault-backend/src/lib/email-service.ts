import { Logger } from './monitoring'
import { config } from '../config'

export interface EmailTemplate {
  subject: string
  htmlContent: string
  textContent: string
}

export interface EmailNotificationData {
  to: string
  subject: string
  template: 'achievement' | 'reminder' | 'shared_list_invite' | 'progress_update' | 'system'
  data: any
}

class EmailService {
  private isInitialized = false
  private apiKey?: string
  private fromEmail: string = '<EMAIL>'
  private fromName: string = 'DreamVault'

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing email service...')
      
      // Initialize with SendGrid, AWS SES, or similar service
      this.apiKey = process.env.SENDGRID_API_KEY || process.env.AWS_SES_ACCESS_KEY
      this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
      this.fromName = process.env.FROM_NAME || 'DreamVault'

      if (!this.apiKey) {
        Logger.warn('Email service API key not configured - email notifications will be disabled')
      }

      this.isInitialized = true
      Logger.info('Email service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize email service', error)
      this.isInitialized = false
    }
  }

  /**
   * Send email notification
   */
  async sendNotification(data: EmailNotificationData): Promise<boolean> {
    try {
      if (!this.apiKey) {
        Logger.warn('Email service not configured, skipping email')
        return false
      }

      const template = this.getEmailTemplate(data.template, data.data)
      
      const emailPayload = {
        to: data.to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: data.subject || template.subject,
        html: template.htmlContent,
        text: template.textContent
      }

      // In a real implementation, you would use SendGrid, AWS SES, or similar
      Logger.info('Email notification would be sent', {
        to: data.to,
        subject: emailPayload.subject,
        template: data.template
      })

      // TODO: Implement actual email sending
      // Example with SendGrid:
      // const sgMail = require('@sendgrid/mail')
      // sgMail.setApiKey(this.apiKey)
      // await sgMail.send(emailPayload)

      return true
    } catch (error) {
      Logger.error('Failed to send email notification', error)
      return false
    }
  }

  /**
   * Get email template based on notification type
   */
  private getEmailTemplate(template: string, data: any): EmailTemplate {
    switch (template) {
      case 'achievement':
        return this.getAchievementTemplate(data)
      case 'reminder':
        return this.getReminderTemplate(data)
      case 'shared_list_invite':
        return this.getSharedListInviteTemplate(data)
      case 'progress_update':
        return this.getProgressUpdateTemplate(data)
      case 'system':
        return this.getSystemTemplate(data)
      default:
        return this.getDefaultTemplate(data)
    }
  }

  /**
   * Achievement notification template
   */
  private getAchievementTemplate(data: any): EmailTemplate {
    return {
      subject: `🎉 Achievement Unlocked: ${data.achievementName}!`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; color: white;">
            <h1 style="margin: 0; font-size: 28px;">🎉 Achievement Unlocked!</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333; margin-top: 0;">${data.achievementName}</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              ${data.achievementDescription}
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">What you accomplished:</h3>
              <p style="color: #666;">${data.details}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.WEB_APP_URL}/dashboard" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View Your Progress
              </a>
            </div>
          </div>
          
          <div style="padding: 20px; text-align: center; color: #999; font-size: 14px;">
            Keep up the great work! 🌟
          </div>
        </div>
      `,
      textContent: `
🎉 Achievement Unlocked: ${data.achievementName}!

${data.achievementDescription}

What you accomplished:
${data.details}

View your progress at: ${process.env.WEB_APP_URL}/dashboard

Keep up the great work! 🌟
      `
    }
  }

  /**
   * Reminder notification template
   */
  private getReminderTemplate(data: any): EmailTemplate {
    return {
      subject: `⏰ Perfect time for: ${data.itemTitle}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px; color: #333;">⏰ Perfect Timing!</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333; margin-top: 0;">${data.itemTitle}</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              ${data.reason}
            </p>
            
            ${data.context ? `
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Why now?</h3>
              <p style="color: #666;">${data.context.details}</p>
            </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.WEB_APP_URL}/dashboard" 
                 style="background: #fcb69f; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Take Action Now
              </a>
            </div>
          </div>
          
          <div style="padding: 20px; text-align: center; color: #999; font-size: 14px;">
            Make today count! ✨
          </div>
        </div>
      `,
      textContent: `
⏰ Perfect time for: ${data.itemTitle}

${data.reason}

${data.context ? `Why now? ${data.context.details}` : ''}

Take action now: ${process.env.WEB_APP_URL}/dashboard

Make today count! ✨
      `
    }
  }

  /**
   * Shared list invite template
   */
  private getSharedListInviteTemplate(data: any): EmailTemplate {
    return {
      subject: `🤝 ${data.inviterName} invited you to collaborate on their bucket list`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px; color: #333;">🤝 You're Invited!</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              <strong>${data.inviterName}</strong> has invited you to collaborate on their bucket list: 
              <strong>"${data.listName}"</strong>
            </p>
            
            ${data.message ? `
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Personal message:</h3>
              <p style="color: #666; font-style: italic;">"${data.message}"</p>
            </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.WEB_APP_URL}/shared-lists/${data.listId}" 
                 style="background: #a8edea; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;">
                Accept Invitation
              </a>
              <a href="${process.env.WEB_APP_URL}/register" 
                 style="background: #fed6e3; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Join DreamVault
              </a>
            </div>
          </div>
          
          <div style="padding: 20px; text-align: center; color: #999; font-size: 14px;">
            Start your shared adventure! 🌟
          </div>
        </div>
      `,
      textContent: `
🤝 You're Invited!

${data.inviterName} has invited you to collaborate on their bucket list: "${data.listName}"

${data.message ? `Personal message: "${data.message}"` : ''}

Accept invitation: ${process.env.WEB_APP_URL}/shared-lists/${data.listId}
Join DreamVault: ${process.env.WEB_APP_URL}/register

Start your shared adventure! 🌟
      `
    }
  }

  /**
   * Progress update template
   */
  private getProgressUpdateTemplate(data: any): EmailTemplate {
    return {
      subject: `📈 Progress update: ${data.itemTitle}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px; color: #333;">📈 Progress Update</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333; margin-top: 0;">${data.itemTitle}</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              ${data.updateMessage}
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Latest Update:</h3>
              <p style="color: #666;">${data.progressNote}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.WEB_APP_URL}/items/${data.itemId}" 
                 style="background: #84fab0; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View Details
              </a>
            </div>
          </div>
          
          <div style="padding: 20px; text-align: center; color: #999; font-size: 14px;">
            Keep making progress! 💪
          </div>
        </div>
      `,
      textContent: `
📈 Progress Update: ${data.itemTitle}

${data.updateMessage}

Latest Update:
${data.progressNote}

View details: ${process.env.WEB_APP_URL}/items/${data.itemId}

Keep making progress! 💪
      `
    }
  }

  /**
   * System notification template
   */
  private getSystemTemplate(data: any): EmailTemplate {
    return {
      subject: data.subject || 'DreamVault System Notification',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #667eea; padding: 30px; text-align: center; color: white;">
            <h1 style="margin: 0; font-size: 28px;">DreamVault</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <h2 style="color: #333; margin-top: 0;">${data.title}</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              ${data.message}
            </p>
            
            ${data.actionUrl ? `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${data.actionUrl}" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                ${data.actionText || 'Learn More'}
              </a>
            </div>
            ` : ''}
          </div>
          
          <div style="padding: 20px; text-align: center; color: #999; font-size: 14px;">
            Thank you for using DreamVault!
          </div>
        </div>
      `,
      textContent: `
DreamVault System Notification

${data.title}

${data.message}

${data.actionUrl ? `${data.actionText || 'Learn More'}: ${data.actionUrl}` : ''}

Thank you for using DreamVault!
      `
    }
  }

  /**
   * Default template
   */
  private getDefaultTemplate(data: any): EmailTemplate {
    return {
      subject: data.subject || 'DreamVault Notification',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #667eea; padding: 30px; text-align: center; color: white;">
            <h1 style="margin: 0; font-size: 28px;">DreamVault</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              ${data.message || 'You have a new notification from DreamVault.'}
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.WEB_APP_URL}/dashboard" 
                 style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View Dashboard
              </a>
            </div>
          </div>
        </div>
      `,
      textContent: `
DreamVault Notification

${data.message || 'You have a new notification from DreamVault.'}

View your dashboard: ${process.env.WEB_APP_URL}/dashboard
      `
    }
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isInitialized && !!this.apiKey
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isAvailable: this.isAvailable(),
      configured: !!this.apiKey,
      fromEmail: this.fromEmail,
      fromName: this.fromName
    }
  }
}

// Export singleton instance
export const emailService = new EmailService()
