import * as Sentry from '@sentry/node'
// import { nodeProfilingIntegration } from '@sentry/profiling-node'
import { config } from '../config'

// Initialize Sentry
export function initSentry() {
  if (!config.monitoring.sentryDsn) {
    console.warn('Sentry DSN not configured, error reporting disabled')
    return
  }

  Sentry.init({
    dsn: config.monitoring.sentryDsn,
    environment: config.server.nodeEnv,
    integrations: [
      // Enable profiling
      // new ProfilingIntegration(),
    ],
    // Performance Monitoring
    tracesSampleRate: config.server.isDevelopment ? 1.0 : 0.1,
    // Profiling
    profilesSampleRate: config.server.isDevelopment ? 1.0 : 0.1,
    // Release tracking
    release: process.env.npm_package_version || '1.0.0',
    // Error filtering
    beforeSend(event, hint) {
      // Don't send validation errors to Sentry in production
      if (event.exception?.values?.[0]?.type === 'ValidationError' && !config.server.isDevelopment) {
        return null
      }
      
      // Don't send 401 errors to Sentry
      if (event.exception?.values?.[0]?.type === 'UnauthorizedError') {
        return null
      }
      
      return event
    },
    // Additional context
    initialScope: {
      tags: {
        component: 'backend-api',
        version: process.env.npm_package_version || '1.0.0'
      }
    }
  })

  console.log('✅ Sentry initialized for error reporting')
}

// Helper function to capture exceptions with context
export function captureException(error: Error, context?: {
  user?: { id: string; email?: string }
  request?: { method: string; url: string; ip?: string }
  extra?: Record<string, any>
}) {
  Sentry.withScope((scope) => {
    if (context?.user) {
      scope.setUser({
        id: context.user.id,
        email: context.user.email
      })
    }
    
    if (context?.request) {
      scope.setTag('method', context.request.method)
      scope.setTag('url', context.request.url)
      scope.setContext('request', context.request)
    }
    
    if (context?.extra) {
      scope.setContext('extra', context.extra)
    }
    
    Sentry.captureException(error)
  })
}

// Helper function to capture messages
export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('context', context)
    }
    Sentry.captureMessage(message, level)
  })
}

// Express error handler middleware for Sentry
export const sentryErrorHandler = (error: any, req: any, res: any, next: any) => {
  // Only send 5xx errors to Sentry
  if (error.status >= 500) {
    Sentry.captureException(error)
  }
  next(error)
}

// Express request handler middleware for Sentry
export const sentryRequestHandler = (req: any, res: any, next: any) => {
  // Basic request tracking
  Sentry.addBreadcrumb({
    message: `${req.method} ${req.url}`,
    category: 'http',
    level: 'info'
  })
  next()
}

export { Sentry }
