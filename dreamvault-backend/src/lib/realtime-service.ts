// Simplified realtime service stub for Supabase migration
import { Logger } from './monitoring'

export interface SocketEventData {
  goalCreated: { goalId: string; userId: string }
  goalUpdated: { goalId: string; userId: string }
  goalCompleted: { goalId: string; userId: string }
  progressUpdated: { goalId: string; userId: string; progress: number }
}

class RealtimeService {
  private isInitialized = false

  async initialize(): Promise<void> {
    Logger.info('Realtime service initialized (stub)')
    this.isInitialized = true
  }

  async emitToUser<K extends keyof SocketEventData>(
    userId: string,
    event: K,
    data: SocketEventData[K]
  ): Promise<void> {
    Logger.info('Realtime event emitted (stub)', { userId, event, data })
  }

  async emitToRoom(room: string, event: string, data: any): Promise<void> {
    Logger.info('Realtime room event emitted (stub)', { room, event, data })
  }

  async joinUserToRoom(userId: string, room: string): Promise<void> {
    Logger.info('User joined room (stub)', { userId, room })
  }

  async leaveUserFromRoom(userId: string, room: string): Promise<void> {
    Logger.info('User left room (stub)', { userId, room })
  }

  isReady(): boolean {
    return this.isInitialized
  }

  async shutdown(): Promise<void> {
    Logger.info('Realtime service shutdown (stub)')
    this.isInitialized = false
  }
}

export const realtimeService = new RealtimeService()
export default realtimeService
