import { Logger } from './monitoring'
import { config } from '../config'

export interface PushNotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

export interface DeviceToken {
  userId: string
  token: string
  platform: 'ios' | 'android' | 'web'
  deviceId?: string
  isActive: boolean
}

class PushNotificationService {
  private isInitialized = false
  private fcmServerKey?: string
  private apnsKeyId?: string
  private apnsTeamId?: string
  private apnsPrivateKey?: string

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      Logger.info('Initializing push notification service...')
      
      // Initialize FCM (Firebase Cloud Messaging) for Android and Web
      this.fcmServerKey = process.env.FCM_SERVER_KEY
      
      // Initialize APNS (Apple Push Notification Service) for iOS
      this.apnsKeyId = process.env.APNS_KEY_ID
      this.apnsTeamId = process.env.APNS_TEAM_ID
      this.apnsPrivateKey = process.env.APNS_PRIVATE_KEY

      if (!this.fcmServerKey) {
        Logger.warn('FCM server key not configured - Android/Web push notifications will be disabled')
      }

      if (!this.apnsKeyId || !this.apnsTeamId || !this.apnsPrivateKey) {
        Logger.warn('APNS credentials not configured - iOS push notifications will be disabled')
      }

      this.isInitialized = true
      Logger.info('Push notification service initialized successfully')
    } catch (error) {
      Logger.error('Failed to initialize push notification service', error)
      this.isInitialized = false
    }
  }

  /**
   * Send push notification to a specific device token
   */
  async sendToDevice(token: string, payload: PushNotificationPayload, platform: 'ios' | 'android' | 'web'): Promise<boolean> {
    try {
      switch (platform) {
        case 'android':
        case 'web':
          return await this.sendFCMNotification(token, payload)
        case 'ios':
          return await this.sendAPNSNotification(token, payload)
        default:
          throw new Error(`Unsupported platform: ${platform}`)
      }
    } catch (error) {
      Logger.error('Failed to send push notification', error)
      return false
    }
  }

  /**
   * Send push notification to multiple devices
   */
  async sendToMultipleDevices(tokens: DeviceToken[], payload: PushNotificationPayload): Promise<{ success: number; failed: number }> {
    let success = 0
    let failed = 0

    const promises = tokens.map(async (deviceToken) => {
      try {
        const result = await this.sendToDevice(deviceToken.token, payload, deviceToken.platform)
        if (result) {
          success++
        } else {
          failed++
        }
      } catch (error) {
        Logger.error('Failed to send to device', { deviceToken: deviceToken.token, error })
        failed++
      }
    })

    await Promise.all(promises)

    Logger.info('Bulk push notification results', { success, failed, total: tokens.length })
    return { success, failed }
  }

  /**
   * Send FCM notification (Android/Web)
   */
  private async sendFCMNotification(token: string, payload: PushNotificationPayload): Promise<boolean> {
    if (!this.fcmServerKey) {
      Logger.warn('FCM not configured, skipping notification')
      return false
    }

    try {
      const fcmPayload = {
        to: token,
        notification: {
          title: payload.title,
          body: payload.body,
          icon: payload.icon || '/icon-192x192.png',
          badge: payload.badge || '/icon-192x192.png',
          click_action: 'FCM_PLUGIN_ACTIVITY'
        },
        data: payload.data || {},
        android: {
          notification: {
            sound: 'default',
            priority: 'high',
            vibrate_timings: ['100ms', '50ms', '100ms']
          }
        },
        webpush: {
          notification: {
            icon: payload.icon || '/icon-192x192.png',
            badge: payload.badge || '/icon-192x192.png',
            vibrate: [100, 50, 100],
            actions: payload.actions || [
              {
                action: 'view',
                title: 'View'
              },
              {
                action: 'dismiss',
                title: 'Dismiss'
              }
            ]
          }
        }
      }

      // In a real implementation, you would use the Firebase Admin SDK
      // For now, we'll simulate the API call
      Logger.info('FCM notification would be sent', {
        token: token.substring(0, 20) + '...',
        title: payload.title
      })

      // TODO: Implement actual FCM API call
      // const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      //   method: 'POST',
      //   headers: {
      //     'Authorization': `key=${this.fcmServerKey}`,
      //     'Content-Type': 'application/json'
      //   },
      //   body: JSON.stringify(fcmPayload)
      // })

      return true
    } catch (error) {
      Logger.error('FCM notification failed', error)
      return false
    }
  }

  /**
   * Send APNS notification (iOS)
   */
  private async sendAPNSNotification(token: string, payload: PushNotificationPayload): Promise<boolean> {
    if (!this.apnsKeyId || !this.apnsTeamId || !this.apnsPrivateKey) {
      Logger.warn('APNS not configured, skipping notification')
      return false
    }

    try {
      const apnsPayload = {
        aps: {
          alert: {
            title: payload.title,
            body: payload.body
          },
          badge: 1,
          sound: 'default',
          'mutable-content': 1
        },
        data: payload.data || {}
      }

      // In a real implementation, you would use the node-apn library or similar
      Logger.info('APNS notification would be sent', {
        token: token.substring(0, 20) + '...',
        title: payload.title
      })

      // TODO: Implement actual APNS API call
      // const apn = require('apn')
      // const provider = new apn.Provider({
      //   token: {
      //     key: this.apnsPrivateKey,
      //     keyId: this.apnsKeyId,
      //     teamId: this.apnsTeamId
      //   },
      //   production: process.env.NODE_ENV === 'production'
      // })

      return true
    } catch (error) {
      Logger.error('APNS notification failed', error)
      return false
    }
  }

  /**
   * Register a device token
   */
  async registerDeviceToken(deviceToken: DeviceToken): Promise<void> {
    try {
      // In a real implementation, you would store this in the database
      // For now, we'll just log it
      Logger.info('Device token registered', {
        userId: deviceToken.userId,
        platform: deviceToken.platform,
        token: deviceToken.token.substring(0, 20) + '...'
      })

      // TODO: Store in database
      // await prisma.deviceToken.upsert({
      //   where: {
      //     userId_deviceId: {
      //       userId: deviceToken.userId,
      //       deviceId: deviceToken.deviceId || 'default'
      //     }
      //   },
      //   update: {
      //     token: deviceToken.token,
      //     platform: deviceToken.platform,
      //     isActive: deviceToken.isActive,
      //     updatedAt: new Date()
      //   },
      //   create: deviceToken
      // })
    } catch (error) {
      Logger.error('Failed to register device token', error)
      throw new Error('Failed to register device token')
    }
  }

  /**
   * Unregister a device token
   */
  async unregisterDeviceToken(userId: string, deviceId: string): Promise<void> {
    try {
      Logger.info('Device token unregistered', { userId, deviceId })

      // TODO: Remove from database
      // await prisma.deviceToken.updateMany({
      //   where: {
      //     userId,
      //     deviceId
      //   },
      //   data: {
      //     isActive: false,
      //     updatedAt: new Date()
      //   }
      // })
    } catch (error) {
      Logger.error('Failed to unregister device token', error)
      throw new Error('Failed to unregister device token')
    }
  }

  /**
   * Get active device tokens for a user
   */
  async getUserDeviceTokens(userId: string): Promise<DeviceToken[]> {
    try {
      // TODO: Fetch from database
      // const tokens = await prisma.deviceToken.findMany({
      //   where: {
      //     userId,
      //     isActive: true
      //   }
      // })

      // For now, return empty array
      Logger.info('Getting device tokens for user', { userId })
      return []
    } catch (error) {
      Logger.error('Failed to get user device tokens', error)
      throw new Error('Failed to get user device tokens')
    }
  }

  /**
   * Send notification to all user devices
   */
  async sendToUser(userId: string, payload: PushNotificationPayload): Promise<{ success: number; failed: number }> {
    try {
      const deviceTokens = await this.getUserDeviceTokens(userId)
      
      if (deviceTokens.length === 0) {
        Logger.info('No device tokens found for user', { userId })
        return { success: 0, failed: 0 }
      }

      return await this.sendToMultipleDevices(deviceTokens, payload)
    } catch (error) {
      Logger.error('Failed to send notification to user', error)
      return { success: 0, failed: 1 }
    }
  }

  /**
   * Validate device token format
   */
  validateDeviceToken(token: string, platform: 'ios' | 'android' | 'web'): boolean {
    if (!token || typeof token !== 'string') {
      return false
    }

    switch (platform) {
      case 'ios':
        // APNS tokens are 64 characters hex
        return /^[a-fA-F0-9]{64}$/.test(token)
      case 'android':
        // FCM tokens are variable length, typically 140+ characters
        return token.length >= 140 && /^[a-zA-Z0-9_-]+$/.test(token)
      case 'web':
        // Web push tokens are base64url encoded
        return token.length >= 100 && /^[a-zA-Z0-9_-]+$/.test(token)
      default:
        return false
    }
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isInitialized
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isAvailable: this.isInitialized,
      fcmConfigured: !!this.fcmServerKey,
      apnsConfigured: !!(this.apnsKeyId && this.apnsTeamId && this.apnsPrivateKey),
      platforms: {
        android: !!this.fcmServerKey,
        ios: !!(this.apnsKeyId && this.apnsTeamId && this.apnsPrivateKey),
        web: !!this.fcmServerKey
      }
    }
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService()
