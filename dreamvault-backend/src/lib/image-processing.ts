import sharp from 'sharp'
import { Logger } from './monitoring'
import { captureException } from './sentry'

export interface ImageProcessingOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
  progressive?: boolean
  stripMetadata?: boolean
}

export interface ProcessedImage {
  buffer: Buffer
  format: string
  width: number
  height: number
  size: number
  contentType: string
}

export interface ImageVariant {
  name: string
  buffer: Buffer
  width: number
  height: number
  size: number
  contentType: string
}

class ImageProcessingService {
  /**
   * Process a single image with the given options
   */
  async processImage(
    inputBuffer: Buffer,
    options: ImageProcessingOptions = {}
  ): Promise<ProcessedImage> {
    try {
      const {
        maxWidth = 1920,
        maxHeight = 1080,
        quality = 85,
        format = 'jpeg',
        progressive = true,
        stripMetadata = true
      } = options

      let pipeline = sharp(inputBuffer)

      // Strip metadata for privacy and smaller file size
      if (stripMetadata) {
        pipeline = pipeline.withMetadata({
          exif: {},
          icc: undefined,
          // xmp: undefined // Removed as not supported in current Sharp version
        })
      }

      // Resize if needed
      pipeline = pipeline.resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })

      // Apply format-specific optimizations
      switch (format) {
        case 'jpeg':
          pipeline = pipeline.jpeg({
            quality,
            progressive,
            mozjpeg: true
          })
          break
        case 'png':
          pipeline = pipeline.png({
            quality,
            progressive,
            compressionLevel: 9
          })
          break
        case 'webp':
          pipeline = pipeline.webp({
            quality,
            effort: 6
          })
          break
      }

      const buffer = await pipeline.toBuffer()
      const metadata = await sharp(buffer).metadata()

      const contentType = this.getContentType(format)

      Logger.info('Image processed successfully', {
        originalSize: inputBuffer.length,
        processedSize: buffer.length,
        width: metadata.width,
        height: metadata.height,
        format,
        compressionRatio: ((inputBuffer.length - buffer.length) / inputBuffer.length * 100).toFixed(2) + '%'
      })

      return {
        buffer,
        format,
        width: metadata.width || 0,
        height: metadata.height || 0,
        size: buffer.length,
        contentType
      }
    } catch (error) {
      Logger.error('Failed to process image', error)
      captureException(error as Error, {
        extra: { options, inputSize: inputBuffer.length }
      })
      throw new Error('Image processing failed')
    }
  }

  /**
   * Create multiple variants of an image (thumbnail, medium, large)
   */
  async createImageVariants(inputBuffer: Buffer): Promise<ImageVariant[]> {
    try {
      const variants: Array<{ name: string; options: ImageProcessingOptions }> = [
        {
          name: 'thumbnail',
          options: {
            maxWidth: 150,
            maxHeight: 150,
            quality: 80,
            format: 'jpeg'
          }
        },
        {
          name: 'small',
          options: {
            maxWidth: 400,
            maxHeight: 400,
            quality: 85,
            format: 'jpeg'
          }
        },
        {
          name: 'medium',
          options: {
            maxWidth: 800,
            maxHeight: 600,
            quality: 85,
            format: 'jpeg'
          }
        },
        {
          name: 'large',
          options: {
            maxWidth: 1920,
            maxHeight: 1080,
            quality: 90,
            format: 'jpeg'
          }
        }
      ]

      const processedVariants = await Promise.all(
        variants.map(async (variant) => {
          const processed = await this.processImage(inputBuffer, variant.options)
          return {
            name: variant.name,
            buffer: processed.buffer,
            width: processed.width,
            height: processed.height,
            size: processed.size,
            contentType: processed.contentType
          }
        })
      )

      Logger.info('Image variants created', {
        originalSize: inputBuffer.length,
        variants: processedVariants.map(v => ({
          name: v.name,
          size: v.size,
          dimensions: `${v.width}x${v.height}`
        }))
      })

      return processedVariants
    } catch (error) {
      Logger.error('Failed to create image variants', error)
      captureException(error as Error, {
        extra: { inputSize: inputBuffer.length }
      })
      throw new Error('Image variant creation failed')
    }
  }

  /**
   * Validate if the buffer contains a valid image
   */
  async validateImage(buffer: Buffer): Promise<{
    isValid: boolean
    format?: string
    width?: number
    height?: number
    error?: string
  }> {
    try {
      const metadata = await sharp(buffer).metadata()
      
      if (!metadata.format || !metadata.width || !metadata.height) {
        return {
          isValid: false,
          error: 'Invalid image format or corrupted file'
        }
      }

      // Check for reasonable dimensions
      if (metadata.width > 10000 || metadata.height > 10000) {
        return {
          isValid: false,
          error: 'Image dimensions too large'
        }
      }

      if (metadata.width < 1 || metadata.height < 1) {
        return {
          isValid: false,
          error: 'Image dimensions too small'
        }
      }

      return {
        isValid: true,
        format: metadata.format,
        width: metadata.width,
        height: metadata.height
      }
    } catch (error) {
      return {
        isValid: false,
        error: 'Failed to process image file'
      }
    }
  }

  /**
   * Extract image metadata
   */
  async getImageMetadata(buffer: Buffer): Promise<{
    format: string
    width: number
    height: number
    channels: number
    density?: number
    hasAlpha: boolean
    isAnimated?: boolean
  }> {
    try {
      const metadata = await sharp(buffer).metadata()
      
      return {
        format: metadata.format || 'unknown',
        width: metadata.width || 0,
        height: metadata.height || 0,
        channels: metadata.channels || 0,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha || false,
        isAnimated: metadata.pages ? metadata.pages > 1 : false
      }
    } catch (error) {
      Logger.error('Failed to extract image metadata', error)
      throw new Error('Failed to extract image metadata')
    }
  }

  /**
   * Get content type for image format
   */
  private getContentType(format: string): string {
    const contentTypes: Record<string, string> = {
      'jpeg': 'image/jpeg',
      'jpg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'gif': 'image/gif',
      'tiff': 'image/tiff',
      'svg': 'image/svg+xml'
    }

    return contentTypes[format.toLowerCase()] || 'application/octet-stream'
  }

  /**
   * Check if file is an image based on buffer
   */
  async isImage(buffer: Buffer): Promise<boolean> {
    try {
      await sharp(buffer).metadata()
      return true
    } catch {
      return false
    }
  }

  /**
   * Convert image to different format
   */
  async convertFormat(
    inputBuffer: Buffer,
    targetFormat: 'jpeg' | 'png' | 'webp',
    quality: number = 85
  ): Promise<ProcessedImage> {
    return this.processImage(inputBuffer, {
      format: targetFormat,
      quality,
      stripMetadata: true
    })
  }
}

// Singleton instance
export const imageProcessingService = new ImageProcessingService()
export default imageProcessingService
