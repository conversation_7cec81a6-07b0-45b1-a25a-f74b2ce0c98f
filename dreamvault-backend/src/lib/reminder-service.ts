// Simplified reminder service stub for Supabase migration
import { Logger } from './monitoring'

export interface ReminderData {
  userId: string
  goalId: string
  title: string
  message: string
  scheduledFor: Date
  type: 'daily' | 'weekly' | 'custom'
}

class ReminderService {
  async createReminder(data: ReminderData): Promise<{ id: string }> {
    Logger.info('Reminder created (stub)', { userId: data.userId, goalId: data.goalId })
    return { id: `reminder_${Date.now()}` }
  }

  async updateReminder(id: string, data: Partial<ReminderData>): Promise<boolean> {
    Logger.info('Reminder updated (stub)', { id, data })
    return true
  }

  async deleteReminder(id: string): Promise<boolean> {
    Logger.info('Reminder deleted (stub)', { id })
    return true
  }

  async getUserReminders(userId: string): Promise<any[]> {
    Logger.info('Getting user reminders (stub)', { userId })
    return []
  }

  async processScheduledReminders(): Promise<void> {
    Logger.info('Processing scheduled reminders (stub)')
  }

  async scheduleGoalReminder(goalId: string, userId: string): Promise<boolean> {
    Logger.info('Goal reminder scheduled (stub)', { goalId, userId })
    return true
  }
}

export const reminderService = new ReminderService()
export default reminderService
