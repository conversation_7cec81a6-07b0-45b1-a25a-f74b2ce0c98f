import { Request, Response, NextFunction } from 'express'
import rateLimitLib from 'express-rate-limit'
import { v4 as uuidv4 } from 'uuid'
import { config } from '../config'

// Request ID middleware
export const requestId = (req: Request, res: Response, next: NextFunction) => {
  const id = req.headers['x-request-id'] as string || uuidv4()
  req.headers['x-request-id'] = id
  res.setHeader('x-request-id', id)
  next()
}

// Rate limiting middleware
export const rateLimit = (options?: {
  windowMs?: number
  max?: number
  message?: string
}) => {
  return rateLimitLib({
    windowMs: options?.windowMs || config.security.rateLimitWindowMs,
    max: options?.max || config.security.rateLimitMax,
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: options?.message || 'Too many requests, please try again later',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return (req as any).user?.id || req.ip
    }
  })
}

// Body sanitization middleware
export const sanitizeBody = (req: Request, res: Response, next: NextFunction) => {
  if (req.body && typeof req.body === 'object') {
    req.body = sanitizeObject(req.body)
  }
  next()
}

// Sanitize object recursively
function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return sanitizeString(obj)
  }
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject)
  }
  if (obj && typeof obj === 'object') {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value)
    }
    return sanitized
  }
  return obj
}

// Basic string sanitization
function sanitizeString(str: string): string {
  return str
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim()
}
