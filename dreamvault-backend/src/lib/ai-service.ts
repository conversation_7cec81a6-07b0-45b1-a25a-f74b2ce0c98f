import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { config } from '../config'
import { Logger } from './monitoring'

export interface CategorySuggestion {
  category: string
  confidence: number
  reasoning: string
}

export interface LocationExtraction {
  locations: Array<{
    name: string
    type: 'city' | 'country' | 'landmark' | 'region'
    confidence: number
  }>
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export interface TagSuggestion {
  tag: string
  relevance: number
  category: string
}

export interface ContentModerationResult {
  isAppropriate: boolean
  flaggedCategories: string[]
  severity: 'low' | 'medium' | 'high'
  explanation: string
}

export interface SmartSuggestions {
  categories: CategorySuggestion[]
  tags: TagSuggestion[]
  locations: LocationExtraction
  estimatedDuration?: string
  estimatedCost?: string
  bestTimeToVisit?: string
  difficulty?: 'easy' | 'medium' | 'hard'
}

class AIService {
  private openai: OpenAI | null = null
  private googleAI: GoogleGenerativeAI | null = null
  private isConfigured: boolean = false
  private useGoogle: boolean = false

  constructor() {
    this.initializeAI()
  }

  private initializeAI() {
    try {
      const googleApiKey = config.externalApis.googleApiKey
      const openaiApiKey = config.externalApis.openaiApiKey

      Logger.info(`Google API key status: ${googleApiKey ? 'present' : 'missing'} (length: ${googleApiKey?.length || 0})`)
      Logger.info(`OpenAI API key status: ${openaiApiKey ? 'present' : 'missing'} (length: ${openaiApiKey?.length || 0})`)

      // Initialize both services if available
      if (googleApiKey) {
        this.googleAI = new GoogleGenerativeAI(googleApiKey)
        Logger.info('Google AI service initialized successfully')
      }

      if (openaiApiKey) {
        this.openai = new OpenAI({
          apiKey: openaiApiKey
        })
        Logger.info('OpenAI service initialized successfully')
      }

      // Prefer Google AI if available, but fallback to OpenAI
      if (this.googleAI) {
        this.useGoogle = true
        this.isConfigured = true
        Logger.info('Using Google AI as primary service')
        return
      }

      if (this.openai) {
        this.useGoogle = false
        this.isConfigured = true
        Logger.info('Using OpenAI as primary service')
        return
      }

      Logger.warn('No AI API keys configured - AI features will be disabled')
      this.isConfigured = false
    } catch (error) {
      Logger.error('Failed to initialize AI service', error)
      this.isConfigured = false
    }
  }

  /**
   * Check if AI service is available
   */
  isAvailable(): boolean {
    return this.isConfigured && (this.openai !== null || this.googleAI !== null)
  }

  /**
   * Generate category suggestions for a bucket list item
   */
  async suggestCategories(title: string, description?: string): Promise<CategorySuggestion[]> {
    if (!this.isAvailable()) {
      throw new Error('AI service not available')
    }

    try {
      const prompt = `
Analyze this bucket list item and suggest 3-5 relevant categories:

Title: "${title}"
Description: "${description || 'No description provided'}"

Available categories: Travel, Adventure, Learning, Creative, Health & Fitness, Career, Relationships, Personal Growth, Entertainment, Food & Drink, Sports, Technology, Art & Culture, Nature, Spiritual, Financial, Social Impact

Respond with a JSON array of objects with this structure:
{
  "category": "category name",
  "confidence": 0.95,
  "reasoning": "brief explanation why this category fits"
}

Only suggest categories that are highly relevant. Confidence should be between 0.1 and 1.0.
`

      let content: string

      // Try Google AI first, fallback to OpenAI if it fails
      if (this.useGoogle && this.googleAI) {
        try {
          const model = this.googleAI.getGenerativeModel({ model: "gemini-2.0-flash" })
          const result = await model.generateContent(prompt)
          const response = await result.response
          content = response.text().trim()
          Logger.info('Used Google AI for category suggestions')
        } catch (googleError) {
          Logger.warn('Google AI failed, falling back to OpenAI', googleError)
          if (config.externalApis.openaiApiKey) {
            Logger.info('Attempting OpenAI request with fetch API')
            Logger.info(`API key length: ${config.externalApis.openaiApiKey.length}`)
            Logger.info(`API key starts with: ${config.externalApis.openaiApiKey.substring(0, 20)}...`)
            Logger.info(`Direct env var length: ${process.env.OPENAI_API_KEY?.length || 0}`)
            Logger.info(`Direct env var starts with: ${process.env.OPENAI_API_KEY?.substring(0, 20) || 'undefined'}...`)
            try {
              const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${config.externalApis.openaiApiKey}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  model: 'gpt-3.5-turbo',
                  messages: [{ role: 'user', content: prompt }],
                  temperature: 0.3,
                  max_tokens: 500
                })
              })

              if (!response.ok) {
                throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
              }

              const data = await response.json()
              content = (data as any).choices[0]?.message?.content?.trim() || ''
              Logger.info('Used OpenAI fallback for category suggestions')
            } catch (openaiError) {
              Logger.error('OpenAI request failed', openaiError)
              throw openaiError
            }
          } else {
            throw new Error('Both Google AI and OpenAI failed')
          }
        }
      } else if (this.openai) {
        const response = await this.openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 500
        })
        content = response.choices[0]?.message?.content?.trim() || ''
        Logger.info('Used OpenAI for category suggestions')
      } else {
        throw new Error('No AI service available')
      }

      if (!content) {
        throw new Error('No response from AI service')
      }

      Logger.info('Raw AI response for category suggestions', { content, provider: this.useGoogle ? 'Google' : 'OpenAI' })

      // Clean up the content to extract JSON
      let jsonContent = content.trim()

      // Remove any markdown code blocks
      if (jsonContent.startsWith('```json')) {
        jsonContent = jsonContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (jsonContent.startsWith('```')) {
        jsonContent = jsonContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      // Find JSON array in the response
      const jsonMatch = jsonContent.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        jsonContent = jsonMatch[0]
      }

      Logger.info('Cleaned JSON content for parsing', { jsonContent })

      const suggestions = JSON.parse(jsonContent) as CategorySuggestion[]
      
      Logger.info('Generated category suggestions', {
        title,
        suggestionsCount: suggestions.length
      })

      return suggestions.filter(s => s.confidence >= 0.5).slice(0, 5)
    } catch (error) {
      Logger.error('Failed to generate category suggestions', error)
      throw new Error('Failed to generate category suggestions')
    }
  }

  /**
   * Extract location information from text
   */
  async extractLocations(text: string): Promise<LocationExtraction> {
    if (!this.isAvailable()) {
      throw new Error('AI service not available')
    }

    try {
      const prompt = `
Extract location information from this text:

"${text}"

Identify any cities, countries, landmarks, or regions mentioned. Respond with JSON:
{
  "locations": [
    {
      "name": "location name",
      "type": "city|country|landmark|region",
      "confidence": 0.95
    }
  ]
}

Only include locations you're confident about (confidence > 0.7).
`

      let content: string

      try {
        if (this.useGoogle && this.googleAI) {
          const model = this.googleAI.getGenerativeModel({ model: "gemini-2.0-flash" })
          const result = await model.generateContent(prompt)
          const response = await result.response
          content = response.text().trim()
          Logger.info('Used Google AI for location extraction')
        } else {
          throw new Error('Google AI not available')
        }
      } catch (googleError) {
        Logger.warn('Google AI failed, falling back to OpenAI', googleError)
        if (config.externalApis.openaiApiKey) {
          Logger.info('Attempting OpenAI request with fetch API')
          try {
            const response = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${config.externalApis.openaiApiKey}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.2,
                max_tokens: 300
              })
            })

            if (!response.ok) {
              throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
            }

            const data = await response.json()
            content = (data as any).choices[0]?.message?.content?.trim() || ''
            Logger.info('Used OpenAI fallback for location extraction')
          } catch (openaiError) {
            Logger.error('OpenAI request failed', openaiError)
            throw openaiError
          }
        } else {
          throw new Error('Both Google AI and OpenAI failed')
        }
      }

      if (!content) {
        throw new Error('No response from AI service')
      }

      Logger.info('Raw AI response for location extraction', { content, provider: this.useGoogle ? 'Google' : 'OpenAI' })

      // Clean up the content to extract JSON
      let jsonContent = content.trim()

      // Remove any markdown code blocks
      if (jsonContent.startsWith('```json')) {
        jsonContent = jsonContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (jsonContent.startsWith('```')) {
        jsonContent = jsonContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      // Find JSON object in the response
      const jsonMatch = jsonContent.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonContent = jsonMatch[0]
      } else {
        // If no JSON found, return empty result
        Logger.warn('No JSON found in AI response, returning empty locations', { content })
        return { locations: [] }
      }

      Logger.info('Cleaned JSON content for parsing', { jsonContent })

      let extraction: LocationExtraction
      try {
        extraction = JSON.parse(jsonContent) as LocationExtraction
      } catch (parseError) {
        Logger.warn('Failed to parse JSON, returning empty locations', { jsonContent, parseError })
        return { locations: [] }
      }
      
      Logger.info('Extracted locations', {
        text: text.substring(0, 100),
        locationsCount: extraction.locations.length
      })

      return extraction
    } catch (error) {
      Logger.error('Failed to extract locations', error)
      throw new Error('Failed to extract locations')
    }
  }

  /**
   * Generate smart tag suggestions
   */
  async suggestTags(title: string, description?: string, category?: string): Promise<TagSuggestion[]> {
    if (!this.isAvailable()) {
      throw new Error('AI service not available')
    }

    try {
      const prompt = `
Generate relevant tags for this bucket list item:

Title: "${title}"
Description: "${description || 'No description provided'}"
Category: "${category || 'Not specified'}"

Suggest 5-10 specific, relevant tags that would help organize and discover this item.
Avoid generic tags. Focus on specific activities, themes, or characteristics.

Respond with JSON array:
[
  {
    "tag": "tag name",
    "relevance": 0.95,
    "category": "activity|theme|location|difficulty|season|etc"
  }
]

Relevance should be between 0.1 and 1.0.
`

      let content: string

      try {
        if (this.useGoogle && this.googleAI) {
          const model = this.googleAI.getGenerativeModel({ model: "gemini-2.0-flash" })
          const result = await model.generateContent(prompt)
          const response = await result.response
          content = response.text().trim()
          Logger.info('Used Google AI for tag suggestions')
        } else {
          throw new Error('Google AI not available')
        }
      } catch (googleError) {
        Logger.warn('Google AI failed, falling back to OpenAI', googleError)
        if (config.externalApis.openaiApiKey) {
          Logger.info('Attempting OpenAI request with fetch API')
          try {
            const response = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${config.externalApis.openaiApiKey}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.4,
                max_tokens: 400
              })
            })

            if (!response.ok) {
              throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
            }

            const data = await response.json()
            content = (data as any).choices[0]?.message?.content?.trim() || ''
            Logger.info('Used OpenAI fallback for tag suggestions')
          } catch (openaiError) {
            Logger.error('OpenAI request failed', openaiError)
            throw openaiError
          }
        } else {
          throw new Error('Both Google AI and OpenAI failed')
        }
      }

      if (!content) {
        throw new Error('No response from AI service')
      }

      Logger.info('Raw AI response for tag suggestions', { content, provider: this.useGoogle ? 'Google' : 'OpenAI' })

      // Clean up the content to extract JSON
      let jsonContent = content.trim()

      // Remove any markdown code blocks
      if (jsonContent.startsWith('```json')) {
        jsonContent = jsonContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (jsonContent.startsWith('```')) {
        jsonContent = jsonContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      // Find JSON array in the response
      const jsonMatch = jsonContent.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        jsonContent = jsonMatch[0]
      }

      Logger.info('Cleaned JSON content for parsing', { jsonContent })

      const suggestions = JSON.parse(jsonContent) as TagSuggestion[]
      
      Logger.info('Generated tag suggestions', {
        title,
        suggestionsCount: suggestions.length
      })

      return suggestions.filter(s => s.relevance >= 0.6).slice(0, 10)
    } catch (error) {
      Logger.error('Failed to generate tag suggestions', error)
      throw new Error('Failed to generate tag suggestions')
    }
  }

  /**
   * Moderate content for appropriateness
   */
  async moderateContent(text: string): Promise<ContentModerationResult> {
    if (!this.isAvailable()) {
      throw new Error('AI service not available')
    }

    try {
      // Use OpenAI's moderation endpoint if available
      if (!this.openai) {
        // Fallback: return safe result if OpenAI not available
        Logger.warn('OpenAI not available for moderation, returning safe result')
        return {
          isAppropriate: true,
          flaggedCategories: [],
          severity: 'low' as const,
          explanation: 'Content moderation unavailable, assuming appropriate'
        }
      }

      const moderation = await this.openai.moderations.create({
        input: text
      })

      const result = moderation.results[0]
      if (!result) {
        throw new Error('No moderation result received')
      }

      const flaggedCategories = Object.entries(result.categories)
        .filter(([_, flagged]) => flagged)
        .map(([category, _]) => category)

      const highestScore = Math.max(...Object.values(result.category_scores))

      let severity: 'low' | 'medium' | 'high' = 'low'
      if (highestScore > 0.8) severity = 'high'
      else if (highestScore > 0.5) severity = 'medium'

      const moderationResult: ContentModerationResult = {
        isAppropriate: !result.flagged,
        flaggedCategories,
        severity,
        explanation: result.flagged
          ? `Content flagged for: ${flaggedCategories.join(', ')}`
          : 'Content appears appropriate'
      }

      Logger.info('Content moderation completed', {
        textLength: text.length,
        isAppropriate: moderationResult.isAppropriate,
        flaggedCategories: flaggedCategories.length
      })

      return moderationResult
    } catch (error) {
      Logger.error('Failed to moderate content', error)

      // Fallback: Basic content moderation using simple rules
      Logger.warn('Using fallback content moderation')
      const lowerText = text.toLowerCase()
      const inappropriateWords = ['spam', 'scam', 'hate', 'violence', 'illegal']
      const hasInappropriateContent = inappropriateWords.some(word => lowerText.includes(word))

      return {
        isAppropriate: !hasInappropriateContent,
        flaggedCategories: hasInappropriateContent ? ['inappropriate'] : [],
        severity: hasInappropriateContent ? 'medium' as const : 'low' as const,
        explanation: hasInappropriateContent
          ? 'Content flagged by basic moderation rules'
          : 'Content appears appropriate (basic check)'
      }
    }
  }

  /**
   * Generate comprehensive smart suggestions for a bucket list item
   */
  async generateSmartSuggestions(title: string, description?: string): Promise<SmartSuggestions> {
    if (!this.isAvailable()) {
      throw new Error('AI service not available')
    }

    try {
      // Run multiple AI operations in parallel
      const [categories, locations, tags] = await Promise.all([
        this.suggestCategories(title, description),
        this.extractLocations(`${title} ${description || ''}`),
        this.suggestTags(title, description)
      ])

      // Generate additional insights
      const insights = await this.generateInsights(title, description)

      const suggestions: SmartSuggestions = {
        categories,
        tags,
        locations,
        ...insights
      }

      Logger.info('Generated comprehensive smart suggestions', {
        title,
        categoriesCount: categories.length,
        tagsCount: tags.length,
        locationsCount: locations.locations.length
      })

      return suggestions
    } catch (error) {
      Logger.error('Failed to generate smart suggestions', error)
      throw new Error('Failed to generate smart suggestions')
    }
  }

  /**
   * Generate additional insights about a bucket list item
   */
  private async generateInsights(title: string, description?: string): Promise<{
    estimatedDuration?: string
    estimatedCost?: string
    bestTimeToVisit?: string
    difficulty?: 'easy' | 'medium' | 'hard'
  }> {
    try {
      const prompt = `
Analyze this bucket list item and provide practical insights:

Title: "${title}"
Description: "${description || 'No description provided'}"

Provide estimates for:
1. Duration (how long it typically takes)
2. Cost range (budget estimate)
3. Best time to visit/do (if applicable)
4. Difficulty level (easy/medium/hard)

Respond with JSON:
{
  "estimatedDuration": "duration estimate",
  "estimatedCost": "cost range",
  "bestTimeToVisit": "timing advice",
  "difficulty": "easy|medium|hard"
}

If any field doesn't apply, omit it from the response.
`

      let content: string

      if (this.useGoogle && this.googleAI) {
        const model = this.googleAI.getGenerativeModel({ model: "gemini-2.0-flash" })
        const result = await model.generateContent(prompt)
        const response = await result.response
        content = response.text().trim()
      } else if (this.openai) {
        const response = await this.openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 300
        })
        content = response.choices[0]?.message?.content?.trim() || ''
      } else {
        return {}
      }

      if (!content) {
        return {}
      }

      Logger.info('Raw AI response for insights', { content, provider: this.useGoogle ? 'Google' : 'OpenAI' })

      // Clean up the content to extract JSON
      let jsonContent = content.trim()

      // Remove any markdown code blocks
      if (jsonContent.startsWith('```json')) {
        jsonContent = jsonContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (jsonContent.startsWith('```')) {
        jsonContent = jsonContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      // Find JSON object in the response
      const jsonMatch = jsonContent.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonContent = jsonMatch[0]
      }

      Logger.info('Cleaned JSON content for parsing', { jsonContent })

      return JSON.parse(jsonContent)
    } catch (error) {
      Logger.warn('Failed to generate insights', error)
      return {}
    }
  }
}

// Singleton instance
export const aiService = new AIService()
export default aiService
