// Simplified notification service stub for Supabase migration
import { Logger } from './monitoring'

// Simplified types
enum NotificationType {
  GOAL_REMINDER = 'goal_reminder',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  SOCIAL_ACTIVITY = 'social_activity'
}

export interface CreateNotificationData {
  userId: string
  type: NotificationType
  title: string
  message: string
  data?: any
}

class NotificationService {
  async createNotification(data: CreateNotificationData): Promise<{ id: string }> {
    Logger.info('Notification created (stub)', { userId: data.userId, type: data.type })
    return { id: `notification_${Date.now()}` }
  }

  async sendPushNotification(userId: string, title: string, message: string): Promise<boolean> {
    Logger.info('Push notification sent (stub)', { userId, title })
    return true
  }

  async sendEmail(userId: string, subject: string, content: string): Promise<boolean> {
    Logger.info('Email sent (stub)', { userId, subject })
    return true
  }

  async markAsRead(notificationId: string): Promise<boolean> {
    Logger.info('Notification marked as read (stub)', { notificationId })
    return true
  }

  async getUserNotifications(userId: string): Promise<any[]> {
    Logger.info('Getting user notifications (stub)', { userId })
    return []
  }
}

export const notificationService = new NotificationService()
export default notificationService
