# Bucket List Items API Documentation

This document describes the implemented CRUD operations for bucket list items in the DreamVault API.

## Overview

The bucket list items API provides comprehensive functionality for managing user bucket list items, including:

- ✅ Full CRUD operations (Create, Read, Update, Delete)
- ✅ Advanced filtering and search capabilities
- ✅ Pagination support
- ✅ Category and priority management
- ✅ Status tracking system
- ✅ Custom tagging and labeling
- ✅ Location support
- ✅ Cost estimation features
- ✅ Media file management
- ✅ Progress tracking and milestones

## Authentication

All endpoints require authentication via JWT token in the Authorization header:

```
Authorization: Bearer <jwt-token>
```

## Core Endpoints

### 1. Create Bucket List Item

**POST** `/api/items`

Creates a new bucket list item for the authenticated user.

**Request Body:**

```json
{
  "title": "Visit Tokyo",
  "description": "Experience the culture and food of Japan",
  "category": "TRAVEL",
  "priority": "MUST_DO",
  "status": "NOT_STARTED",
  "targetDate": "2024-12-31T00:00:00Z",
  "estimatedCost": 3000,
  "location": {
    "name": "Tokyo, Japan",
    "latitude": 35.6762,
    "longitude": 139.6503,
    "address": "Tokyo, Japan",
    "country": "Japan",
    "city": "Tokyo"
  },
  "tags": ["travel", "culture", "food"]
}
```

**Response (201):**

```json
{
  "message": "Bucket list item created successfully",
  "item": {
    "id": "clx1234567890",
    "title": "Visit Tokyo",
    "description": "Experience the culture and food of Japan",
    "category": "TRAVEL",
    "priority": "MUST_DO",
    "status": "NOT_STARTED",
    "targetDate": "2024-12-31T00:00:00Z",
    "estimatedCost": 3000,
    "location": { ... },
    "tags": ["travel", "culture", "food"],
    "completionPercentage": 0,
    "completedAt": null,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "mediaFiles": [],
    "progressEntries": [],
    "milestones": []
  }
}
```

### 2. List Bucket List Items

**GET** `/api/items`

Retrieves a paginated list of bucket list items with optional filtering and sorting.

**Query Parameters:**

- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `category` (string): Filter by category (TRAVEL, CAREER, PERSONAL_GROWTH, RELATIONSHIPS, ADVENTURES, LEARNING)
- `priority` (string): Filter by priority (MUST_DO, WANT_TO_DO, SOMEDAY)
- `status` (string): Filter by status (NOT_STARTED, IN_PROGRESS, COMPLETED, ON_HOLD)
- `search` (string): Search in title and description
- `sortBy` (string): Sort field (createdAt, updatedAt, title, targetDate, priority, completionPercentage)
- `sortOrder` (string): Sort order (asc, desc)
- `tags` (string): Comma-separated tags to filter by

**Examples:**

```
GET /api/items?category=TRAVEL&priority=MUST_DO
GET /api/items?search=Tokyo&sortBy=createdAt&sortOrder=desc
GET /api/items?status=IN_PROGRESS&page=2&limit=10
GET /api/items?tags=travel,culture
```

**Response (200):**

```json
{
  "items": [
    {
      "id": "clx1234567890",
      "title": "Visit Tokyo",
      "category": "TRAVEL",
      "priority": "MUST_DO",
      "status": "NOT_STARTED",
      // ... other fields
      "mediaFiles": [...],
      "progressEntries": [...],
      "milestones": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalCount": 45,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### 3. Get Specific Bucket List Item

**GET** `/api/items/:id`

Retrieves a specific bucket list item with full details.

**Response (200):**

```json
{
  "item": {
    "id": "clx1234567890",
    "title": "Visit Tokyo",
    "description": "Experience the culture and food of Japan",
    "category": "TRAVEL",
    "priority": "MUST_DO",
    "status": "NOT_STARTED",
    "targetDate": "2024-12-31T00:00:00Z",
    "estimatedCost": 3000,
    "location": { ... },
    "tags": ["travel", "culture", "food"],
    "completionPercentage": 0,
    "completedAt": null,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "mediaFiles": [...],
    "progressEntries": [...],
    "milestones": [...]
  }
}
```

### 4. Update Bucket List Item

**PUT** `/api/items/:id`

Updates an existing bucket list item. All fields are optional.

**Request Body:**

```json
{
  "title": "Updated Title",
  "status": "IN_PROGRESS",
  "priority": "WANT_TO_DO",
  "estimatedCost": 3500,
  "tags": ["travel", "culture", "food", "adventure"]
}
```

**Response (200):**

```json
{
  "message": "Bucket list item updated successfully",
  "item": {
    // Updated item data
  }
}
```

**Special Behavior:**

- When status is changed to "COMPLETED", `completedAt` is automatically set and `completionPercentage` becomes 100
- When status is changed to "NOT_STARTED", `completionPercentage` becomes 0
- A progress entry is automatically created when an item is marked as completed

### 5. Delete Bucket List Item

**DELETE** `/api/items/:id`

Deletes a bucket list item and all associated data (media files, progress entries, milestones).

**Response (200):**

```json
{
  "message": "Bucket list item deleted successfully"
}
```

## Media Management Endpoints

### Upload Media Files

**POST** `/api/items/:itemId/media`

Uploads media files (images, videos, audio) to a bucket list item.

**Request:** Multipart form data with:

- `files`: Array of files (max 5 files, 10MB each)
- `caption`: Optional caption for the media

**Response (201):**

```json
{
  "message": "Media files uploaded successfully",
  "mediaFiles": [
    {
      "id": "media123",
      "fileUrl": "https://dreamvault-media.s3.amazonaws.com/...",
      "fileType": "IMAGE",
      "fileSize": 1024000,
      "caption": "Beautiful sunset in Tokyo",
      "metadata": { ... },
      "uploadedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Delete Media File

**DELETE** `/api/items/:itemId/media/:mediaId`

Deletes a specific media file from a bucket list item.

**Response (200):**

```json
{
  "message": "Media file deleted successfully"
}
```

## Progress Tracking Endpoints

### Get Progress Data

**GET** `/api/items/:itemId/progress`

Retrieves progress data including completion percentage, progress entries, and milestones.

**Response (200):**

```json
{
  "progress": {
    "completionPercentage": 45,
    "entries": [
      {
        "id": "progress123",
        "description": "Booked flights to Tokyo",
        "entryType": "PROGRESS",
        "entryDate": "2024-01-15T10:30:00Z",
        "mediaFiles": [],
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "milestones": [
      {
        "id": "milestone123",
        "title": "Book Accommodation",
        "description": "Find and book hotel in Shibuya",
        "completedAt": null,
        "mediaFiles": [],
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Update Progress Percentage

**PUT** `/api/items/:itemId/progress`

Updates the completion percentage of a bucket list item.

**Request Body:**

```json
{
  "completionPercentage": 75
}
```

**Response (200):**

```json
{
  "message": "Progress updated successfully",
  "progress": {
    "completionPercentage": 75,
    "status": "IN_PROGRESS",
    "completedAt": null
  }
}
```

### Add Progress Entry

**POST** `/api/items/:itemId/progress/entries`

Adds a new progress entry to track journey milestones.

**Request Body:**

```json
{
  "description": "Visited Tokyo Tower today!",
  "entryType": "PROGRESS",
  "entryDate": "2024-06-15T14:30:00Z"
}
```

**Response (201):**

```json
{
  "message": "Progress entry created successfully",
  "entry": {
    "id": "entry123",
    "description": "Visited Tokyo Tower today!",
    "entryType": "PROGRESS",
    "entryDate": "2024-06-15T14:30:00Z",
    "mediaFiles": [],
    "createdAt": "2024-06-15T14:30:00Z"
  }
}
```

### Create Milestone

**POST** `/api/items/:itemId/milestones`

Creates a new milestone for a bucket list item.

**Request Body:**

```json
{
  "title": "Visit Senso-ji Temple",
  "description": "Explore the historic temple in Asakusa"
}
```

**Response (201):**

```json
{
  "message": "Milestone created successfully",
  "milestone": {
    "id": "milestone123",
    "title": "Visit Senso-ji Temple",
    "description": "Explore the historic temple in Asakusa",
    "completedAt": null,
    "mediaFiles": [],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Update Milestone

**PUT** `/api/items/:itemId/milestones/:milestoneId`

Updates a milestone, including marking it as completed.

**Request Body:**

```json
{
  "title": "Updated milestone title",
  "completedAt": "2024-06-15T16:00:00Z"
}
```

**Response (200):**

```json
{
  "message": "Milestone updated successfully",
  "milestone": {
    // Updated milestone data
  }
}
```

## Data Models

### Categories

- `TRAVEL` - Travel and destinations
- `CAREER` - Professional and career goals
- `PERSONAL_GROWTH` - Self-improvement and learning
- `RELATIONSHIPS` - Social and relationship goals
- `ADVENTURES` - Adventure and outdoor activities
- `LEARNING` - Educational and skill-building goals

### Priorities

- `MUST_DO` - High priority, must accomplish
- `WANT_TO_DO` - Medium priority, would like to accomplish
- `SOMEDAY` - Low priority, someday goals

### Status

- `NOT_STARTED` - Haven't begun working on this goal
- `IN_PROGRESS` - Actively working on this goal
- `COMPLETED` - Goal has been accomplished
- `ON_HOLD` - Temporarily paused

### Media Types

- `IMAGE` - Photos and images
- `VIDEO` - Video files
- `VOICE_NOTE` - Audio recordings
- `DOCUMENT` - Documents and files

### Progress Entry Types

- `PROGRESS` - General progress update
- `MILESTONE` - Milestone achievement
- `COMPLETION` - Goal completion
- `NOTE` - General note or observation

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Additional error details (for validation errors)",
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req-123456"
  }
}
```

### Common Error Codes

- `VALIDATION_FAILED` (400) - Request data validation failed
- `AUTH_TOKEN_MISSING` (401) - No authentication token provided
- `AUTH_TOKEN_INVALID` (401) - Invalid or expired token
- `ITEM_NOT_FOUND` (404) - Bucket list item not found
- `MEDIA_NOT_FOUND` (404) - Media file not found
- `MILESTONE_NOT_FOUND` (404) - Milestone not found
- `INTERNAL_SERVER_ERROR` (500) - Server error

## Implementation Status

✅ **Completed Features:**

- Full CRUD operations for bucket list items
- Advanced filtering and search functionality
- Pagination support
- Category assignment and priority levels
- Status tracking system (Not Started, In Progress, Completed, On Hold)
- Target date and cost estimation features
- Custom tagging and labeling system
- Location support with coordinates
- Media file upload and management
- Progress tracking with percentage and entries
- Milestone creation and management
- Comprehensive error handling
- Input validation and sanitization
- User ownership verification
- Automatic progress entry creation for completions

This implementation fully satisfies the requirements specified in task 5 of the DreamVault core specification.
