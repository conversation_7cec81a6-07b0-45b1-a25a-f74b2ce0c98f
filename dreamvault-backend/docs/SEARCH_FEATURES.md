# Search and Organization Features

This document describes the search and organization features implemented for the DreamVault bucket list application.

## Overview

The search functionality provides comprehensive filtering, sorting, and organization capabilities for bucket list items. It includes both basic search operations and advanced full-text search capabilities using PostgreSQL.

## API Endpoints

### 1. Advanced Search - `/api/search`

**GET /api/search**

Provides comprehensive search and filtering capabilities with the following features:

#### Query Parameters

**Text Search:**

- `query` - Text search across title and description

**Filters:**

- `category[]` - Filter by one or more categories (TRAVEL, CAREER, PERSONAL_GROWTH, RELATIONSHIPS, ADVENTURES, LEARNING)
- `priority[]` - Filter by priority levels (MUST_DO, WANT_TO_DO, SOMEDAY)
- `status[]` - Filter by status (NOT_STARTED, IN_PROGRESS, COMPLETED, ON_HOLD)
- `tags[]` - Filter by tags (all specified tags must be present)

**Date Filters:**

- `createdAfter` / `createdBefore` - Filter by creation date
- `targetDateAfter` / `targetDateBefore` - Filter by target completion date
- `completedAfter` / `completedBefore` - Filter by completion date

**Numeric Filters:**

- `minCost` / `maxCost` - Filter by estimated cost range
- `minCompletion` / `maxCompletion` - Filter by completion percentage (0-100)

**Location Filters:**

- `location.name` - Filter by location name
- `location.country` - Filter by country
- `location.city` - Filter by city

**Sorting:**

- `sortBy` - Sort field (createdAt, updatedAt, title, targetDate, priority, completionPercentage, estimatedCost, completedAt, relevance)
- `sortOrder` - Sort direction (asc, desc)

**Pagination:**

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

**Options:**

- `includeCompleted` - Include completed items (default: true)
- `fuzzySearch` - Enable fuzzy text matching (default: false)

#### Response Format

```json
{
  "items": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalCount": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "facets": {
    "categories": { "TRAVEL": 45, "LEARNING": 32, ... },
    "priorities": { "MUST_DO": 23, "WANT_TO_DO": 67, ... },
    "statuses": { "NOT_STARTED": 89, "IN_PROGRESS": 34, ... }
  },
  "searchMeta": {
    "query": "travel",
    "appliedFilters": {...},
    "sortBy": "createdAt",
    "sortOrder": "desc",
    "fuzzySearch": false
  }
}
```

### 2. Search Suggestions - `/api/search/suggestions`

**GET /api/search/suggestions**

Provides autocomplete suggestions for search queries.

#### Query Parameters

- `query` - Search query (required)
- `type` - Suggestion type: 'all', 'titles', 'tags', 'locations' (default: 'all')

#### Response Format

```json
{
  "query": "travel",
  "suggestions": {
    "titles": ["Travel to Japan", "Travel Europe"],
    "tags": ["travel", "traveling", "travel-photography"],
    "locations": [{ "name": "Tokyo", "country": "Japan", "city": "Tokyo" }]
  }
}
```

### 3. Full-Text Search - `/api/search/fulltext`

**GET /api/search/fulltext**

Uses PostgreSQL's full-text search capabilities for advanced text matching.

#### Query Parameters

- `query` - Search query (required)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 50)
- `includeDescription` - Include description in search (default: true)
- `highlightResults` - Return highlighted search results (default: false)

#### Response Format

```json
{
  "items": [
    {
      "id": "...",
      "title": "Learn JavaScript",
      "searchRank": 0.8,
      "highlightedContent": "Learn <b>JavaScript</b> programming...",
      ...
    }
  ],
  "pagination": {...},
  "searchMeta": {
    "query": "javascript",
    "searchType": "fulltext",
    "includeDescription": true,
    "highlightResults": false
  }
}
```

### 4. Autocomplete - `/api/search/autocomplete`

**GET /api/search/autocomplete**

Provides real-time autocomplete suggestions using full-text search.

#### Query Parameters

- `query` - Search query (minimum 2 characters)
- `limit` - Maximum suggestions (default: 10)

#### Response Format

```json
{
  "query": "learn",
  "suggestions": [
    { "title": "Learn Python Programming", "rank": 0.9 },
    { "title": "Learn Guitar", "rank": 0.7 }
  ]
}
```

### 5. Enhanced Items Endpoint - `/api/items`

The existing items endpoint has been enhanced with additional filtering and sorting capabilities:

#### New Query Parameters

- `createdAfter` / `createdBefore` - Date range filters
- `targetDateAfter` / `targetDateBefore` - Target date filters
- `minCost` / `maxCost` - Cost range filters
- `minCompletion` / `maxCompletion` - Completion percentage filters
- `location` - Location name search

#### Enhanced Sorting

- `estimatedCost` - Sort by estimated cost
- `completedAt` - Sort by completion date
- Improved priority sorting with proper ordering

## Database Optimizations

### Indexes Created

The following indexes have been added for optimal search performance:

1. **Full-text search indexes:**

   - `bucket_list_items_title_idx` - GIN index on title
   - `bucket_list_items_description_idx` - GIN index on description

2. **Composite indexes for filtering:**

   - `bucket_list_items_user_category_status_idx` - (user_id, category, status)
   - `bucket_list_items_user_priority_status_idx` - (user_id, priority, status)

3. **Date-based indexes:**

   - `bucket_list_items_user_created_at_idx` - (user_id, created_at)
   - `bucket_list_items_user_target_date_idx` - (user_id, target_date)
   - `bucket_list_items_user_completed_at_idx` - (user_id, completed_at)

4. **Numeric filters:**

   - `bucket_list_items_user_estimated_cost_idx` - (user_id, estimated_cost)
   - `bucket_list_items_user_completion_idx` - (user_id, completion_percentage)

5. **JSON field indexes:**

   - `bucket_list_items_tags_idx` - GIN index on tags array
   - `bucket_list_items_location_idx` - GIN index on location JSON

6. **Sorting indexes:**
   - `bucket_list_items_user_priority_created_idx` - (user_id, priority, created_at)
   - `bucket_list_items_user_updated_at_idx` - (user_id, updated_at)

## Analytics Endpoints

### Dashboard Analytics - `/api/analytics/dashboard`

**GET /api/analytics/dashboard**

Provides comprehensive analytics for the user's bucket list.

#### Query Parameters

- `timeframe` - Time period: 'week', 'month', 'quarter', 'year', 'all' (default: 'all')
- `groupBy` - Grouping: 'category', 'priority', 'status', 'month', 'week' (default: 'status')

#### Response Format

```json
{
  "summary": {
    "totalItems": 150,
    "completedItems": 45,
    "inProgressItems": 23,
    "completionRate": 30.0,
    "avgCompletionDays": 45
  },
  "breakdowns": {
    "category": [...],
    "priority": [...],
    "status": [...]
  },
  "trends": {
    "completion": [
      { "month": "2024-01-01", "completedCount": 5 }
    ]
  },
  "recentActivity": [...],
  "popularTags": [
    { "tag": "travel", "count": 23 }
  ]
}
```

### Search Statistics - `/api/analytics/search-stats`

**GET /api/analytics/search-stats**

Provides statistics about search-related data quality.

#### Response Format

```json
{
  "searchability": {
    "totalItems": 150,
    "itemsWithTags": 120,
    "itemsWithLocation": 67,
    "itemsWithMedia": 89,
    "avgTagsPerItem": 3.2,
    "taggingRate": 80.0,
    "locationRate": 44.7,
    "mediaRate": 59.3
  },
  "popularCategories": [...]
}
```

## Features Implemented

✅ **Advanced search functionality with filtering by:**

- Category, status, priority
- Location (name, country, city)
- Date ranges (created, target, completed)
- Cost ranges
- Completion percentage ranges
- Tags

✅ **Sorting options by:**

- Priority (with proper custom ordering)
- Date added (createdAt)
- Deadline (targetDate)
- Completion rate (completionPercentage)
- Estimated cost
- Completion date
- Relevance (for text search)

✅ **Efficient database queries with proper indexing:**

- Full-text search indexes using PostgreSQL GIN
- Composite indexes for common filter combinations
- JSON field indexes for tags and location
- Optimized sorting indexes

✅ **Full-text search capabilities:**

- PostgreSQL native full-text search
- Search ranking and relevance scoring
- Highlighted search results
- Prefix matching for autocomplete

✅ **Pagination for large item lists:**

- Configurable page size (max 100 items)
- Complete pagination metadata
- Efficient offset-based pagination

## Additional Features

- **Faceted search** - Returns count of items in each category/priority/status for filtering UI
- **Search suggestions** - Autocomplete for titles, tags, and locations
- **Analytics integration** - Search statistics and usage analytics
- **Performance optimized** - Comprehensive database indexing strategy
- **Type-safe** - Full TypeScript support with shared type definitions

## Usage Examples

### Basic Search

```
GET /api/search?query=travel&category=TRAVEL&status=NOT_STARTED
```

### Advanced Filtering

```
GET /api/search?minCost=1000&maxCost=5000&createdAfter=2024-01-01&sortBy=priority&sortOrder=asc
```

### Full-Text Search

```
GET /api/search/fulltext?query=learn programming&highlightResults=true
```

### Autocomplete

```
GET /api/search/autocomplete?query=trav
```

This implementation provides a comprehensive search and organization system that meets all the requirements specified in the task.
