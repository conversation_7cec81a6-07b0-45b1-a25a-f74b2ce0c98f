# DreamVault Storage Setup Guide

This guide walks you through setting up Supabase storage buckets and policies for the DreamVault application.

## Overview

DreamVault uses Supabase Storage for handling media files including:
- User-uploaded images for bucket list items
- Profile avatars
- Progress photos
- Voice notes and documents

## Required Storage Buckets

### 1. media-files
- **Purpose**: Store all media files related to bucket list items
- **File Size Limit**: 50MB per file
- **Allowed Types**: Images, videos, audio, PDFs
- **Public Access**: Yes (with RLS policies)

### 2. avatars
- **Purpose**: Store user profile pictures
- **File Size Limit**: 5MB per file
- **Allowed Types**: Images only
- **Public Access**: Yes (with RLS policies)

## Manual Setup Instructions

### Step 1: Create Storage Buckets

1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the sidebar
3. Click **Create Bucket**

#### Create media-files bucket:
- **Name**: `media-files`
- **Public bucket**: ✅ Enabled
- **File size limit**: `52428800` (50MB)
- **Allowed MIME types**: 
  ```
  image/jpeg, image/png, image/gif, image/webp,
  video/mp4, video/quicktime,
  audio/mpeg, audio/wav,
  application/pdf
  ```

#### Create avatars bucket:
- **Name**: `avatars`
- **Public bucket**: ✅ Enabled
- **File size limit**: `5242880` (5MB)
- **Allowed MIME types**: 
  ```
  image/jpeg, image/png, image/gif, image/webp
  ```

### Step 2: Set Up RLS Policies

1. Go to **Authentication** > **Policies** in your Supabase dashboard
2. Find the `storage.objects` table
3. Add the following policies:

#### Media Files Policies

**Policy 1: Users can upload media files**
```sql
CREATE POLICY "Users can upload media files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'media-files' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

**Policy 2: Users can view media files**
```sql
CREATE POLICY "Users can view media files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
);
```

**Policy 3: Users can update own media files**
```sql
CREATE POLICY "Users can update own media files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
) WITH CHECK (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

**Policy 4: Users can delete own media files**
```sql
CREATE POLICY "Users can delete own media files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'media-files'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

#### Avatar Policies

**Policy 5: Users can upload avatars**
```sql
CREATE POLICY "Users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

**Policy 6: Users can view avatars**
```sql
CREATE POLICY "Users can view avatars" ON storage.objects
FOR SELECT USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
);
```

**Policy 7: Users can update own avatars**
```sql
CREATE POLICY "Users can update own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
) WITH CHECK (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

**Policy 8: Users can delete own avatars**
```sql
CREATE POLICY "Users can delete own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

### Step 3: Enable Row Level Security

Make sure RLS is enabled on the storage.objects table:

```sql
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
```

### Step 4: Create Helper Functions (Optional)

Add these utility functions to your database:

```sql
-- Function to get file extension
CREATE OR REPLACE FUNCTION get_file_extension(filename text)
RETURNS text AS $$
BEGIN
  RETURN lower(substring(filename from '\.([^.]*)$'));
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique filename
CREATE OR REPLACE FUNCTION generate_unique_filename(user_id uuid, original_filename text)
RETURNS text AS $$
DECLARE
  file_ext text;
  unique_name text;
BEGIN
  file_ext := get_file_extension(original_filename);
  unique_name := user_id::text || '/' || extract(epoch from now())::bigint || '_' || gen_random_uuid()::text;
  
  IF file_ext IS NOT NULL THEN
    unique_name := unique_name || '.' || file_ext;
  END IF;
  
  RETURN unique_name;
END;
$$ LANGUAGE plpgsql;
```

## File Organization Structure

Files are organized in user-specific folders:

```
media-files/
├── {user-id-1}/
│   ├── 1704067200_abc123.jpg
│   ├── 1704067300_def456.mp4
│   └── ...
├── {user-id-2}/
│   ├── 1704067400_ghi789.png
│   └── ...
└── ...

avatars/
├── {user-id-1}/
│   └── avatar_1704067500_jkl012.jpg
├── {user-id-2}/
│   └── avatar_1704067600_mno345.png
└── ...
```

## Testing the Setup

1. **Test bucket creation**: Check that both buckets appear in the Storage section
2. **Test file upload**: Try uploading a file through the mobile app
3. **Test RLS policies**: Ensure users can only access their own files
4. **Test file deletion**: Verify cleanup works correctly

## Troubleshooting

### Common Issues

1. **"Bucket not found" errors**
   - Verify bucket names match exactly: `media-files` and `avatars`
   - Check that buckets are marked as public

2. **"Permission denied" errors**
   - Ensure RLS policies are created correctly
   - Verify user authentication is working
   - Check that file paths include user ID folder

3. **File upload failures**
   - Check file size limits
   - Verify MIME types are allowed
   - Ensure network connectivity

### Verification Commands

Run these in the Supabase SQL editor to verify setup:

```sql
-- Check buckets exist
SELECT * FROM storage.buckets WHERE id IN ('media-files', 'avatars');

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';

-- Check if RLS is enabled
SELECT relname, relrowsecurity FROM pg_class WHERE relname = 'objects';
```

## Security Considerations

- Files are organized by user ID to prevent unauthorized access
- RLS policies ensure users can only access their own files
- File size limits prevent abuse
- MIME type restrictions prevent malicious uploads
- All uploads require authentication

## Next Steps

After completing this setup:
1. Test file uploads from the mobile app
2. Verify image display in the web app
3. Test file deletion and cleanup
4. Monitor storage usage and costs
