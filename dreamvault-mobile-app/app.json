{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "dreamvault-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.dreamvault.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.dreamvault.mobile"}, "web": {"favicon": "./assets/favicon.png"}, "scheme": "dreamvault", "plugins": ["expo-router", "expo-camera", "expo-media-library", "expo-notifications"]}}