# DreamVault Mobile App

A React Native mobile application for the DreamVault bucket list platform, built with Expo and TypeScript.

## Features

- **Cross-platform**: Runs on iOS and Android
- **Authentication**: Supabase Auth integration with email/password
- **Real-time sync**: Data syncs with web application via Supabase
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Offline support**: Works offline with data sync when online
- **Camera integration**: Capture progress photos
- **Push notifications**: Goal reminders and achievement notifications

## Tech Stack

- **React Native** with Expo
- **TypeScript** for type safety
- **Expo Router** for navigation
- **Supabase** for backend services
- **React Query** for data fetching and caching
- **Zustand** for state management
- **React Hook Form + Zod** for form validation

## Project Structure

```
mobile-app/
├── app/                    # Expo Router pages
│   ├── (auth)/            # Authentication screens
│   ├── (tabs)/            # Main app tabs
│   ├── _layout.tsx        # Root layout
│   └── index.tsx          # Entry point
├── src/
│   ├── config/            # Configuration files
│   ├── services/          # API services
│   ├── stores/            # Zustand stores
│   ├── types/             # TypeScript types
│   └── components/        # Reusable components
└── assets/                # Static assets
```

## Getting Started

### Prerequisites

- Node.js 18+
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Update `.env` with your Supabase credentials:
```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_API_URL=http://localhost:3001/api
```

### Development

Start the development server:
```bash
npm start
```

Run on specific platforms:
```bash
npm run ios      # iOS Simulator
npm run android  # Android Emulator
npm run web      # Web browser
```

## Features Implementation Status

### ✅ Completed
- [x] Project setup with Expo and TypeScript
- [x] Authentication flow (login/signup)
- [x] Tab navigation structure
- [x] Supabase integration
- [x] Basic UI components and screens
- [x] State management with Zustand
- [x] API service layer

### 🚧 In Progress
- [ ] Bucket list CRUD operations
- [ ] Progress tracking
- [ ] Media upload and camera integration
- [ ] AI features integration
- [ ] Social features
- [ ] Push notifications
- [ ] Offline sync

## Database Schema

The app uses the same Supabase database as the web application with the following main tables:

- `users` - User profiles and settings
- `bucket_list_items` - Core bucket list functionality
- `achievements` - Gamification system
- `media_files` - File attachments
- `progress_entries` - Progress tracking
- `milestones` - Sub-goals and milestones
- `notifications` - Push notifications

## Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for all new code
3. Add proper error handling
4. Test on both iOS and Android
5. Update documentation as needed

## Deployment

The app can be deployed using Expo Application Services (EAS):

```bash
# Build for production
eas build --platform all

# Submit to app stores
eas submit --platform all
```
