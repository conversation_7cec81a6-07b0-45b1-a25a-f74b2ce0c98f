import { create } from 'zustand'
import { BucketListItem, BucketListCategory, BucketListStatus, BucketListPriority } from '../types/database'
import { bucketListApi } from '../services/api'

interface BucketListState {
  items: BucketListItem[]
  loading: boolean
  error: string | null
  
  // Actions
  fetchItems: () => Promise<void>
  createItem: (item: Omit<BucketListItem, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<BucketListItem | null>
  updateItem: (id: string, updates: Partial<BucketListItem>) => Promise<BucketListItem | null>
  deleteItem: (id: string) => Promise<void>
  clearError: () => void
  
  // Filters and computed values
  getItemsByStatus: (status: BucketListStatus) => BucketListItem[]
  getItemsByCategory: (category: BucketListCategory) => BucketListItem[]
  getCompletedCount: () => number
  getTotalCount: () => number
}

export const useBucketListStore = create<BucketListState>((set, get) => ({
  items: [],
  loading: false,
  error: null,

  fetchItems: async () => {
    set({ loading: true, error: null })
    try {
      const items = await bucketListApi.getItems()
      set({ items, loading: false })
    } catch (error: any) {
      set({ error: error.message || 'Failed to fetch items', loading: false })
    }
  },

  createItem: async (itemData) => {
    console.log('🏪 [Store] Creating item with data:', JSON.stringify(itemData, null, 2))
    set({ loading: true, error: null })
    try {
      const newItem = await bucketListApi.createItem(itemData)
      console.log('✅ [Store] Item created successfully:', newItem)
      set(state => ({
        items: [newItem, ...state.items],
        loading: false
      }))
      return newItem
    } catch (error: any) {
      console.error('❌ [Store] Failed to create item:', error)
      const errorMessage = error.message || 'Failed to create item'
      set({ error: errorMessage, loading: false })
      return null
    }
  },

  updateItem: async (id, updates) => {
    set({ loading: true, error: null })
    try {
      const updatedItem = await bucketListApi.updateItem(id, updates)
      set(state => ({
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        ),
        loading: false
      }))
      return updatedItem
    } catch (error: any) {
      set({ error: error.message || 'Failed to update item', loading: false })
      return null
    }
  },

  deleteItem: async (id) => {
    set({ loading: true, error: null })
    try {
      await bucketListApi.deleteItem(id)
      set(state => ({
        items: state.items.filter(item => item.id !== id),
        loading: false
      }))
    } catch (error: any) {
      set({ error: error.message || 'Failed to delete item', loading: false })
    }
  },

  clearError: () => set({ error: null }),

  // Computed values
  getItemsByStatus: (status) => {
    return get().items.filter(item => item.status === status)
  },

  getItemsByCategory: (category) => {
    return get().items.filter(item => item.category === category)
  },

  getCompletedCount: () => {
    return get().items.filter(item => item.status === 'COMPLETED').length
  },

  getTotalCount: () => {
    return get().items.length
  },
}))
