import { useState } from 'react'
import { View, Text, StyleSheet, Modal, Pressable, Alert, TextInput } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { mediaService } from '../services/mediaService'

interface CameraModalProps {
  visible: boolean
  onClose: () => void
  bucketListItemId: string
  onMediaUploaded: () => void
}

export default function CameraModal({ 
  visible, 
  onClose, 
  bucketListItemId, 
  onMediaUploaded 
}: CameraModalProps) {
  const [uploading, setUploading] = useState(false)
  const [caption, setCaption] = useState('')
  const [showCaptionInput, setShowCaptionInput] = useState(false)
  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(null)

  const handleTakePhoto = async () => {
    try {
      const result = await mediaService.takePhoto()
      if (result && !result.canceled && result.assets[0]) {
        setSelectedImageUri(result.assets[0].uri)
        setShowCaptionInput(true)
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to take photo')
    }
  }

  const handlePickImage = async () => {
    try {
      const result = await mediaService.pickImage()
      if (result && !result.canceled && result.assets[0]) {
        setSelectedImageUri(result.assets[0].uri)
        setShowCaptionInput(true)
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to pick image')
    }
  }

  const handleUpload = async () => {
    if (!selectedImageUri) return

    setUploading(true)
    try {
      const result = await mediaService.uploadImage(
        selectedImageUri,
        bucketListItemId,
        caption.trim() || undefined
      )

      if (result.success) {
        Alert.alert('Success', 'Photo uploaded successfully!')
        onMediaUploaded()
        handleClose()
      } else {
        Alert.alert('Error', result.error || 'Failed to upload photo')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to upload photo')
    } finally {
      setUploading(false)
    }
  }

  const handleClose = () => {
    setSelectedImageUri(null)
    setCaption('')
    setShowCaptionInput(false)
    onClose()
  }

  if (showCaptionInput && selectedImageUri) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.container}>
          <View style={styles.header}>
            <Pressable style={styles.cancelButton} onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </Pressable>
            <Text style={styles.title}>Add Caption</Text>
            <Pressable 
              style={[styles.uploadButton, uploading && styles.uploadButtonDisabled]}
              onPress={handleUpload}
              disabled={uploading}
            >
              <Text style={styles.uploadButtonText}>
                {uploading ? 'Uploading...' : 'Upload'}
              </Text>
            </Pressable>
          </View>

          <View style={styles.content}>
            <View style={styles.imagePreview}>
              <Text style={styles.imagePreviewText}>📸 Photo Selected</Text>
            </View>

            <View style={styles.captionSection}>
              <Text style={styles.label}>Caption (Optional)</Text>
              <TextInput
                style={styles.captionInput}
                value={caption}
                onChangeText={setCaption}
                placeholder="Add a caption for your photo..."
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        </View>
      </Modal>
    )
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Pressable style={styles.cancelButton} onPress={handleClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </Pressable>
          <Text style={styles.title}>Add Photo</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.content}>
          <View style={styles.optionsContainer}>
            <Pressable style={styles.option} onPress={handleTakePhoto}>
              <View style={styles.optionIcon}>
                <Ionicons name="camera" size={32} color="#667eea" />
              </View>
              <Text style={styles.optionTitle}>Take Photo</Text>
              <Text style={styles.optionDescription}>
                Use your camera to capture a new photo
              </Text>
            </Pressable>

            <Pressable style={styles.option} onPress={handlePickImage}>
              <View style={styles.optionIcon}>
                <Ionicons name="images" size={32} color="#667eea" />
              </View>
              <Text style={styles.optionTitle}>Choose from Gallery</Text>
              <Text style={styles.optionDescription}>
                Select an existing photo from your gallery
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    padding: 4,
  },
  cancelButtonText: {
    color: '#667eea',
    fontSize: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  uploadButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  optionsContainer: {
    gap: 20,
  },
  option: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  optionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  optionDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  imagePreview: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    marginBottom: 24,
  },
  imagePreviewText: {
    fontSize: 24,
  },
  captionSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  captionInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    textAlignVertical: 'top',
    minHeight: 80,
  },
})
