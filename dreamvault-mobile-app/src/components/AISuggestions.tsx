import { useState, useEffect, useRef } from 'react'
import { View, Text, StyleSheet, Pressable, ScrollView, ActivityIndicator } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { aiService } from '../services/aiService'
import { BucketListCategory } from '../types/database'

interface AISuggestionsProps {
  title?: string
  description?: string
  onCategorySelect?: (category: BucketListCategory) => void
  onTagSelect?: (tags: string[]) => void
  onLocationSelect?: (location: string) => void
}

export default function AISuggestions({
  title,
  description,
  onCategorySelect,
  onTagSelect,
  onLocationSelect,
}: AISuggestionsProps) {
  const [loading, setLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<{
    suggestedCategory?: string
    suggestedTags: string[]
    extractedLocations: string[]
    isContentAppropriate: boolean
  }>({
    suggestedTags: [],
    extractedLocations: [],
    isContentAppropriate: true,
  })
  const [showSuggestions, setShowSuggestions] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Only trigger AI suggestions if title is long enough
    if (title && title.trim().length > 3) {
      // Debounce the AI call by 1 second to prevent overloading
      timeoutRef.current = setTimeout(() => {
        getSuggestions()
      }, 1000)
    } else {
      // Reset suggestions if title is too short
      setShowSuggestions(false)
      setSuggestions({
        suggestedTags: [],
        extractedLocations: [],
        isContentAppropriate: true,
      })
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [title, description])

  const getSuggestions = async () => {
    if (!title || !title.trim()) return

    // Prevent multiple concurrent requests
    if (loading) return

    setLoading(true)
    try {
      const result = await aiService.enhanceGoalCreation(title, description)

      // Ensure we have valid data structure with fallbacks
      const safeSuggestions = {
        suggestedCategory: result?.suggestedCategory || undefined,
        suggestedTags: Array.isArray(result?.suggestedTags) ? result.suggestedTags : [],
        extractedLocations: Array.isArray(result?.extractedLocations) ? result.extractedLocations : [],
        isContentAppropriate: result?.isContentAppropriate !== false, // Default to true if undefined
      }

      setSuggestions(safeSuggestions)
      setShowSuggestions(true)
    } catch (error) {
      console.error('Failed to get AI suggestions:', error)
      // Reset to safe defaults on error
      setSuggestions({
        suggestedTags: [],
        extractedLocations: [],
        isContentAppropriate: true,
      })
    } finally {
      setLoading(false)
    }
  }

  if (!showSuggestions && !loading) {
    return null
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Ionicons name="sparkles" size={16} color="#667eea" />
          <Text style={styles.title}>AI Suggestions</Text>
        </View>
        {loading && <ActivityIndicator size="small" color="#667eea" />}
        <Pressable 
          style={styles.closeButton}
          onPress={() => setShowSuggestions(false)}
        >
          <Ionicons name="close" size={16} color="#9CA3AF" />
        </Pressable>
      </View>

      {!suggestions.isContentAppropriate && (
        <View style={styles.warningContainer}>
          <Ionicons name="warning" size={16} color="#F59E0B" />
          <Text style={styles.warningText}>
            Content may need review. Please ensure it follows community guidelines.
          </Text>
        </View>
      )}

      {suggestions.suggestedCategory && (
        <View style={styles.suggestionSection}>
          <Text style={styles.sectionTitle}>Suggested Category</Text>
          <Pressable
            style={styles.categoryChip}
            onPress={() => onCategorySelect?.(suggestions.suggestedCategory as BucketListCategory)}
          >
            <Text style={styles.categoryText}>{suggestions.suggestedCategory}</Text>
            <Ionicons name="add" size={16} color="#667eea" />
          </Pressable>
        </View>
      )}

      {suggestions.suggestedTags && suggestions.suggestedTags.length > 0 && (
        <View style={styles.suggestionSection}>
          <Text style={styles.sectionTitle}>Suggested Tags</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.tagsContainer}>
              {suggestions.suggestedTags.map((tag, index) => (
                <Pressable
                  key={index}
                  style={styles.tagChip}
                  onPress={() => onTagSelect?.([tag])}
                >
                  <Text style={styles.tagText}>{tag}</Text>
                  <Ionicons name="add" size={14} color="#667eea" />
                </Pressable>
              ))}
              {suggestions.suggestedTags.length > 1 && (
                <Pressable
                  style={styles.addAllButton}
                  onPress={() => onTagSelect?.(suggestions.suggestedTags)}
                >
                  <Text style={styles.addAllText}>Add All</Text>
                </Pressable>
              )}
            </View>
          </ScrollView>
        </View>
      )}

      {suggestions.extractedLocations && suggestions.extractedLocations.length > 0 && (
        <View style={styles.suggestionSection}>
          <Text style={styles.sectionTitle}>Detected Locations</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.locationsContainer}>
              {suggestions.extractedLocations.map((location, index) => (
                <Pressable
                  key={index}
                  style={styles.locationChip}
                  onPress={() => onLocationSelect?.(location)}
                >
                  <Ionicons name="location" size={14} color="#10B981" />
                  <Text style={styles.locationText}>{location}</Text>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>
      )}

      {!loading && suggestions.suggestedTags.length === 0 && suggestions.extractedLocations.length === 0 && !suggestions.suggestedCategory && (
        <View style={styles.noSuggestionsContainer}>
          <Text style={styles.noSuggestionsText}>
            No suggestions available. Try adding more details to your goal.
          </Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#667eea',
  },
  closeButton: {
    padding: 4,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
    padding: 8,
    marginBottom: 12,
    gap: 6,
  },
  warningText: {
    fontSize: 12,
    color: '#92400E',
    flex: 1,
  },
  suggestionSection: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 6,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
    gap: 6,
  },
  categoryText: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
    paddingRight: 16,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#374151',
  },
  addAllButton: {
    backgroundColor: '#667eea',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 4,
    justifyContent: 'center',
  },
  addAllText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  locationsContainer: {
    flexDirection: 'row',
    gap: 6,
    paddingRight: 16,
  },
  locationChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#10B981',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  locationText: {
    fontSize: 12,
    color: '#10B981',
    fontWeight: '500',
  },
  noSuggestionsContainer: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  noSuggestionsText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
  },
})
