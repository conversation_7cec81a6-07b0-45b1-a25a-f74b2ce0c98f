import AsyncStorage from '@react-native-async-storage/async-storage'
import { BucketListItem } from '../types/database'

const STORAGE_KEYS = {
  BUCKET_LIST_ITEMS: 'bucket_list_items',
  PENDING_ACTIONS: 'pending_actions',
  LAST_SYNC: 'last_sync',
}

export interface PendingAction {
  id: string
  type: 'create' | 'update' | 'delete'
  data: any
  timestamp: number
}

class OfflineSync {
  // Cache bucket list items locally
  async cacheBucketListItems(items: BucketListItem[]) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.BUCKET_LIST_ITEMS, JSON.stringify(items))
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString())
    } catch (error) {
      console.error('Failed to cache bucket list items:', error)
    }
  }

  // Get cached bucket list items
  async getCachedBucketListItems(): Promise<BucketListItem[]> {
    try {
      const cached = await AsyncStorage.getItem(STORAGE_KEYS.BUCKET_LIST_ITEMS)
      return cached ? JSON.parse(cached) : []
    } catch (error) {
      console.error('Failed to get cached bucket list items:', error)
      return []
    }
  }

  // Add pending action for offline operations
  async addPendingAction(action: Omit<PendingAction, 'id' | 'timestamp'>) {
    try {
      const pendingActions = await this.getPendingActions()
      const newAction: PendingAction = {
        ...action,
        id: Date.now().toString(),
        timestamp: Date.now(),
      }
      
      pendingActions.push(newAction)
      await AsyncStorage.setItem(STORAGE_KEYS.PENDING_ACTIONS, JSON.stringify(pendingActions))
    } catch (error) {
      console.error('Failed to add pending action:', error)
    }
  }

  // Get pending actions
  async getPendingActions(): Promise<PendingAction[]> {
    try {
      const pending = await AsyncStorage.getItem(STORAGE_KEYS.PENDING_ACTIONS)
      return pending ? JSON.parse(pending) : []
    } catch (error) {
      console.error('Failed to get pending actions:', error)
      return []
    }
  }

  // Clear pending actions after successful sync
  async clearPendingActions() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.PENDING_ACTIONS)
    } catch (error) {
      console.error('Failed to clear pending actions:', error)
    }
  }

  // Get last sync timestamp
  async getLastSyncTime(): Promise<number> {
    try {
      const lastSync = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC)
      return lastSync ? parseInt(lastSync, 10) : 0
    } catch (error) {
      console.error('Failed to get last sync time:', error)
      return 0
    }
  }

  // Check if data is stale (older than 5 minutes)
  async isDataStale(): Promise<boolean> {
    const lastSync = await this.getLastSyncTime()
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000)
    return lastSync < fiveMinutesAgo
  }

  // Clear all cached data
  async clearCache() {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.BUCKET_LIST_ITEMS,
        STORAGE_KEYS.PENDING_ACTIONS,
        STORAGE_KEYS.LAST_SYNC,
      ])
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }
}

export const offlineSync = new OfflineSync()
