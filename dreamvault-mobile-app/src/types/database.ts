// Database types matching Supabase schema
export interface User {
  id: string
  email: string
  username?: string
  full_name?: string
  avatar_url?: string
  bio?: string
  location?: string
  website?: string
  is_public?: boolean
  created_at?: string
  updated_at?: string
}

export interface BucketListItem {
  id: string
  user_id: string
  title: string
  description?: string
  status?: BucketListStatus
  category: BucketListCategory
  priority?: BucketListPriority
  target_date?: string
  completed_date?: string
  is_public?: boolean
  location?: string
  estimated_cost?: number
  actual_cost?: number
  notes?: string
  tags?: string[]
  created_at?: string
  updated_at?: string
}

export interface Achievement {
  id: string
  user_id: string
  type: AchievementType
  title: string
  description?: string
  icon?: string
  points?: number
  unlocked_at?: string
  bucket_list_item_id?: string
}

export interface MediaFile {
  id: string
  bucket_list_item_id: string
  user_id: string
  file_name: string
  file_path: string
  file_size: number
  mime_type: string
  media_type: MediaType
  alt_text?: string
  caption?: string
  is_primary?: boolean
  created_at?: string
}

export interface ProgressEntry {
  id: string
  bucket_list_item_id: string
  user_id: string
  title: string
  description?: string
  progress_percentage?: number
  created_at?: string
  updated_at?: string
}

export interface Milestone {
  id: string
  bucket_list_item_id: string
  user_id: string
  title: string
  description?: string
  target_date?: string
  completed_date?: string
  is_completed?: boolean
  order_index?: number
  created_at?: string
  updated_at?: string
}

export interface Notification {
  id: string
  user_id: string
  type: NotificationType
  title: string
  message?: string
  is_read?: boolean
  action_url?: string
  created_at?: string
  bucket_list_item_id?: string
  achievement_id?: string
}

// Enums
export type BucketListStatus = 'PLAYING' | 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED'

export type BucketListCategory =
  | 'TRAVEL'
  | 'CAREER'
  | 'PERSONAL'
  | 'RELATIONSHIPS'
  | 'ADVENTURES'
  | 'LEARNING'
  | 'HEALTH'
  | 'CREATIVE'
  | 'FINANCIAL'
  | 'SPIRITUAL'

export type BucketListPriority = 'LOW' | 'MEDIUM' | 'HIGH'

export type AchievementType = 
  | 'first_goal' 
  | 'goal_completed' 
  | 'streak' 
  | 'category_master' 
  | 'social' 
  | 'milestone'

export type MediaType = 'image' | 'video' | 'document'

export type NotificationType = 
  | 'goal_reminder' 
  | 'achievement_unlocked' 
  | 'milestone_completed' 
  | 'social_interaction' 
  | 'system'
