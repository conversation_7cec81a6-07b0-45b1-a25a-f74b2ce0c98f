import * as ImagePicker from 'expo-image-picker'
import * as MediaLibrary from 'expo-media-library'
import { supabase } from '../config/supabase'
import { MediaFile } from '../types/database'

export interface MediaUploadResult {
  success: boolean
  mediaFile?: MediaFile
  error?: string
}

class MediaService {
  // Request permissions for camera and media library
  async requestPermissions(): Promise<boolean> {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync()
      const mediaLibraryPermission = await MediaLibrary.requestPermissionsAsync()
      
      return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted'
    } catch (error) {
      console.error('Error requesting permissions:', error)
      return false
    }
  }

  // Take photo with camera
  async takePhoto(): Promise<ImagePicker.ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestPermissions()
      if (!hasPermission) {
        throw new Error('Camera permission not granted')
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      })

      return result
    } catch (error) {
      console.error('Error taking photo:', error)
      return null
    }
  }

  // Pick image from gallery
  async pickImage(): Promise<ImagePicker.ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestPermissions()
      if (!hasPermission) {
        throw new Error('Media library permission not granted')
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      })

      return result
    } catch (error) {
      console.error('Error picking image:', error)
      return null
    }
  }

  // Upload image to Supabase Storage
  async uploadImage(
    uri: string, 
    bucketListItemId: string, 
    caption?: string
  ): Promise<MediaUploadResult> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }

      // Create file name with timestamp
      const timestamp = Date.now()
      const fileName = `${user.id}/${bucketListItemId}/${timestamp}.jpg`

      // Convert URI to blob for upload
      const response = await fetch(uri)
      const blob = await response.blob()

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('media-files')
        .upload(fileName, blob, {
          contentType: 'image/jpeg',
          upsert: false
        })

      if (uploadError) {
        return { success: false, error: uploadError.message }
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media-files')
        .getPublicUrl(fileName)

      // Save media file record to database
      const { data: mediaFile, error: dbError } = await supabase
        .from('media_files')
        .insert([{
          bucket_list_item_id: bucketListItemId,
          user_id: user.id,
          file_name: fileName.split('/').pop() || fileName,
          file_path: publicUrl,
          file_size: blob.size,
          mime_type: 'image/jpeg',
          media_type: 'image',
          caption: caption || null,
          is_primary: false,
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage.from('media-files').remove([fileName])
        return { success: false, error: dbError.message }
      }

      return { success: true, mediaFile: mediaFile as MediaFile }
    } catch (error: any) {
      return { success: false, error: error.message || 'Upload failed' }
    }
  }

  // Get media files for a bucket list item
  async getMediaFiles(bucketListItemId: string): Promise<MediaFile[]> {
    try {
      const { data, error } = await supabase
        .from('media_files')
        .select('*')
        .eq('bucket_list_item_id', bucketListItemId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching media files:', error)
        return []
      }

      return data as MediaFile[]
    } catch (error) {
      console.error('Error fetching media files:', error)
      return []
    }
  }

  // Delete media file
  async deleteMediaFile(mediaFile: MediaFile): Promise<boolean> {
    try {
      // Delete from storage
      const fileName = mediaFile.file_name
      const { error: storageError } = await supabase.storage
        .from('media-files')
        .remove([fileName])

      if (storageError) {
        console.error('Error deleting from storage:', storageError)
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('media_files')
        .delete()
        .eq('id', mediaFile.id)

      if (dbError) {
        console.error('Error deleting from database:', dbError)
        return false
      }

      return true
    } catch (error) {
      console.error('Error deleting media file:', error)
      return false
    }
  }

  // Set primary image for bucket list item
  async setPrimaryImage(mediaFileId: string, bucketListItemId: string): Promise<boolean> {
    try {
      // First, unset all primary images for this bucket list item
      await supabase
        .from('media_files')
        .update({ is_primary: false })
        .eq('bucket_list_item_id', bucketListItemId)

      // Then set the selected image as primary
      const { error } = await supabase
        .from('media_files')
        .update({ is_primary: true })
        .eq('id', mediaFileId)

      if (error) {
        console.error('Error setting primary image:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error setting primary image:', error)
      return false
    }
  }
}

export const mediaService = new MediaService()
