import { supabase } from '../config/supabase'
import { BucketListItem, User, Achievement, MediaFile, ProgressEntry, Milestone } from '../types/database'

// User API
export const userApi = {
  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (error) throw error
    return data as User
  },

  async updateProfile(updates: Partial<User>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', user.id)
      .select()
      .single()

    if (error) throw error
    return data as User
  },
}

// Bucket List API
export const bucketListApi = {
  async getItems() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from('bucket_list_items')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as BucketListItem[]
  },

  async getItem(id: string) {
    const { data, error } = await supabase
      .from('bucket_list_items')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as BucketListItem
  },

  async createItem(item: Omit<BucketListItem, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    console.log('🚀 [API] Creating bucket list item:', JSON.stringify(item, null, 2))

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      console.error('❌ [API] No authenticated user found')
      throw new Error('No authenticated user')
    }

    console.log('✅ [API] User authenticated:', user.id)

    const itemToInsert = {
      ...item,
      user_id: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('📝 [API] Inserting item to database:', JSON.stringify(itemToInsert, null, 2))

    const { data, error } = await supabase
      .from('bucket_list_items')
      .insert([itemToInsert])
      .select()
      .single()

    if (error) {
      console.error('❌ [API] Database error:', error)
      throw error
    }

    console.log('✅ [API] Item created successfully:', data)
    return data as BucketListItem
  },

  async updateItem(id: string, updates: Partial<BucketListItem>) {
    const { data, error } = await supabase
      .from('bucket_list_items')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as BucketListItem
  },

  async deleteItem(id: string) {
    const { error } = await supabase
      .from('bucket_list_items')
      .delete()
      .eq('id', id)

    if (error) throw error
  },
}

// Achievements API
export const achievementsApi = {
  async getUserAchievements() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .eq('user_id', user.id)
      .order('unlocked_at', { ascending: false })

    if (error) throw error
    return data as Achievement[]
  },
}

// Progress API
export const progressApi = {
  async getProgressEntries(bucketListItemId: string) {
    const { data, error } = await supabase
      .from('progress_entries')
      .select('*')
      .eq('bucket_list_item_id', bucketListItemId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as ProgressEntry[]
  },

  async createProgressEntry(entry: Omit<ProgressEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from('progress_entries')
      .insert([{
        ...entry,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single()

    if (error) throw error
    return data as ProgressEntry
  },
}

// Milestones API
export const milestonesApi = {
  async getMilestones(bucketListItemId: string) {
    const { data, error } = await supabase
      .from('milestones')
      .select('*')
      .eq('bucket_list_item_id', bucketListItemId)
      .order('order_index', { ascending: true })

    if (error) throw error
    return data as Milestone[]
  },

  async createMilestone(milestone: Omit<Milestone, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from('milestones')
      .insert([{
        ...milestone,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single()

    if (error) throw error
    return data as Milestone
  },

  async updateMilestone(id: string, updates: Partial<Milestone>) {
    const { data, error } = await supabase
      .from('milestones')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Milestone
  },
}
