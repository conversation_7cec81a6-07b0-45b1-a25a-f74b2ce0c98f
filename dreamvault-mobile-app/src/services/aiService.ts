import { supabase } from '../config/supabase'

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3001/api'

interface CategorySuggestion {
  category: string
  confidence: number
  reasoning: string
}

interface LocationExtraction {
  locations: Array<{
    name: string
    type: string
    confidence: number
  }>
  confidence: number
}

interface TagSuggestion {
  tags: string[]
  confidence: number
}

interface SmartSuggestion {
  suggestions: Array<{
    title: string
    description: string
    category: string
    reasoning: string
    confidence: number
  }>
}

class AIService {
  private async getAuthHeaders(): Promise<HeadersInit> {
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session?.access_token) {
      throw new Error('No authentication token available')
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    }
  }

  private async makeRequest<T>(endpoint: string, data: any): Promise<T> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error: any) {
      console.error(`AI Service Error (${endpoint}):`, error)
      throw error
    }
  }

  // Get category suggestions for a bucket list item
  async suggestCategories(title: string, description?: string): Promise<CategorySuggestion[]> {
    try {
      const response = await this.makeRequest<{ suggestions: CategorySuggestion[] }>(
        '/ai/suggest-categories',
        { title, description }
      )
      return response.suggestions
    } catch (error: any) {
      console.error('Failed to get category suggestions:', error)
      return []
    }
  }

  // Extract locations from text
  async extractLocations(text: string): Promise<LocationExtraction> {
    try {
      const response = await this.makeRequest<{ extraction: LocationExtraction }>(
        '/ai/extract-locations',
        { text }
      )
      return response.extraction
    } catch (error: any) {
      console.error('Failed to extract locations:', error)
      return { locations: [], confidence: 0 }
    }
  }

  // Get tag suggestions
  async suggestTags(title: string, description?: string, category?: string): Promise<TagSuggestion> {
    try {
      const response = await this.makeRequest<TagSuggestion>(
        '/ai/suggest-tags',
        { title, description, category }
      )
      return response
    } catch (error: any) {
      console.error('Failed to get tag suggestions:', error)
      return { tags: [], confidence: 0 }
    }
  }

  // Moderate content
  async moderateContent(content: string): Promise<{ isAppropriate: boolean; reason?: string }> {
    try {
      const response = await this.makeRequest<{ isAppropriate: boolean; reason?: string }>(
        '/ai/moderate-content',
        { text: content }
      )
      return response
    } catch (error: any) {
      console.error('Failed to moderate content:', error)
      return { isAppropriate: true }
    }
  }

  // Get smart suggestions based on user's existing goals
  async getSmartSuggestions(userGoals: string[], preferences?: any): Promise<SmartSuggestion> {
    try {
      // Convert user goals to a title and description format that the backend expects
      const title = userGoals.length > 0 ? 'Personalized goal suggestions' : 'New goal ideas'
      const description = userGoals.length > 0
        ? `Based on your existing goals: ${userGoals.slice(0, 3).join(', ')}`
        : 'Generate new bucket list ideas'

      const response = await this.makeRequest<{ suggestions: any }>(
        '/ai/smart-suggestions',
        { title, description }
      )

      // Extract all categories from the backend response and convert to SmartSuggestion format
      const categories = response.suggestions?.categories || []
      const suggestions = categories.map((cat: any) => ({
        title: `Explore ${cat.category}`,
        description: cat.reasoning || `Consider adding a ${cat.category.toLowerCase()} goal to your bucket list`,
        category: cat.category,
        reasoning: cat.reasoning,
        confidence: cat.confidence
      }))

      return { suggestions }
    } catch (error: any) {
      console.error('Failed to get smart suggestions:', error)
      return { suggestions: [] }
    }
  }

  // Check AI service status
  async checkStatus(): Promise<{ isAvailable: boolean; features: string[] }> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${API_BASE_URL}/ai/status`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        return { isAvailable: false, features: [] }
      }

      const data = await response.json()
      return {
        isAvailable: data.status?.isAvailable || false,
        features: data.status?.features || []
      }
    } catch (error) {
      console.error('Failed to check AI service status:', error)
      return { isAvailable: false, features: [] }
    }
  }

  // Enhanced goal creation with AI assistance
  async enhanceGoalCreation(title: string, description?: string): Promise<{
    suggestedCategory?: string
    suggestedTags: string[]
    extractedLocations: string[]
    isContentAppropriate: boolean
    enhancedDescription?: string
  }> {
    try {
      const [categoryResult, tagResult, locationResult, moderationResult] = await Promise.allSettled([
        this.suggestCategories(title, description),
        this.suggestTags(title, description),
        this.extractLocations(`${title} ${description || ''}`),
        this.moderateContent(`${title} ${description || ''}`)
      ])

      return {
        suggestedCategory: categoryResult.status === 'fulfilled' && categoryResult.value.length > 0
          ? categoryResult.value[0].category.toUpperCase()
          : undefined,
        suggestedTags: tagResult.status === 'fulfilled' 
          ? tagResult.value.tags 
          : [],
        extractedLocations: locationResult.status === 'fulfilled' 
          ? locationResult.value.locations.map(loc => loc.name)
          : [],
        isContentAppropriate: moderationResult.status === 'fulfilled' 
          ? moderationResult.value.isAppropriate 
          : true,
      }
    } catch (error) {
      console.error('Failed to enhance goal creation:', error)
      return {
        suggestedTags: [],
        extractedLocations: [],
        isContentAppropriate: true,
      }
    }
  }
}

export const aiService = new AIService()
