import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { bucketListApi } from '../services/api'
import { BucketListItem } from '../types/database'
import { supabase } from '../config/supabase'

// Query keys
export const bucketListKeys = {
  all: ['bucketList'] as const,
  lists: () => [...bucketListKeys.all, 'list'] as const,
  list: (filters: string) => [...bucketListKeys.lists(), { filters }] as const,
  details: () => [...bucketListKeys.all, 'detail'] as const,
  detail: (id: string) => [...bucketListKeys.details(), id] as const,
}

// Hooks
export function useBucketListItems() {
  return useQuery({
    queryKey: bucketListKeys.lists(),
    queryFn: bucketListApi.getItems,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useBucketListItem(id: string) {
  return useQuery({
    queryKey: bucketListKeys.detail(id),
    queryFn: () => bucketListApi.getItem(id),
    enabled: !!id,
  })
}

export function useCreateBucketListItem() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: bucketListApi.createItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: bucketListKeys.lists() })
    },
  })
}

export function useUpdateBucketListItem() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<BucketListItem> }) =>
      bucketListApi.updateItem(id, updates),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: bucketListKeys.lists() })
      queryClient.invalidateQueries({ queryKey: bucketListKeys.detail(variables.id) })
    },
  })
}

export function useDeleteBucketListItem() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: bucketListApi.deleteItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: bucketListKeys.lists() })
    },
  })
}

// Real-time subscription hook
export function useRealtimeBucketList() {
  const queryClient = useQueryClient()

  useEffect(() => {
    console.log('Setting up real-time subscription for bucket list items')

    // Subscribe to bucket list changes
    const channel = supabase
      .channel('bucket_list_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bucket_list_items'
        },
        (payload: any) => {
          console.log('Real-time event received:', payload)

          // Invalidate and refetch queries to get fresh data
          queryClient.invalidateQueries({ queryKey: bucketListKeys.lists() })

          // If we have a specific item ID, also invalidate that detail query
          if (payload.new?.id) {
            queryClient.invalidateQueries({ queryKey: bucketListKeys.detail(payload.new.id) })
          }
          if (payload.old?.id) {
            queryClient.invalidateQueries({ queryKey: bucketListKeys.detail(payload.old.id) })
          }
        }
      )
      .subscribe((status) => {
        console.log('Real-time subscription status:', status)
      })

    // Cleanup subscription on unmount
    return () => {
      console.log('Cleaning up real-time subscription')
      supabase.removeChannel(channel)
    }
  }, [queryClient])

  return {
    isConnected: true // Could track actual connection status
  }
}
