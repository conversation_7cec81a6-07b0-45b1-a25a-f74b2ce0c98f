import { useEffect } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { useAuthStore } from '../../src/stores/authStore'
import { useBucketListStore } from '../../src/stores/bucketListStore'
import { useRealtimeBucketList } from '../../src/hooks/useBucketList'

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed': return '#10B981'
    case 'in_progress': return '#3B82F6'
    case 'paused': return '#F59E0B'
    default: return '#6B7280'
  }
}

export default function Dashboard() {
  const { user } = useAuthStore()
  const { items, fetchItems, getTotalCount, getCompletedCount, getItemsByStatus } = useBucketListStore()

  // Set up real-time subscriptions
  useRealtimeBucketList()

  useEffect(() => {
    fetchItems()
  }, [])

  const totalGoals = getTotalCount()
  const completedGoals = getCompletedCount()
  const inProgressGoals = getItemsByStatus('in_progress').length
  const completionRate = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0

  const recentItems = items.slice(0, 3)

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Welcome back!</Text>
            <Text style={styles.userName}>{user?.user_metadata?.full_name || 'Dreamer'}</Text>
          </View>
          <Pressable style={styles.profileButton} onPress={() => router.push('/(tabs)/profile')}>
            <Ionicons name="person-circle-outline" size={32} color="#667eea" />
          </Pressable>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="list" size={24} color="#667eea" />
            <Text style={styles.statNumber}>{totalGoals}</Text>
            <Text style={styles.statLabel}>Total Goals</Text>
          </View>
          <View style={styles.statCard}>
            <Ionicons name="checkmark-circle" size={24} color="#10B981" />
            <Text style={styles.statNumber}>{completedGoals}</Text>
            <Text style={styles.statLabel}>Completed</Text>
          </View>
          <View style={styles.statCard}>
            <Ionicons name="trending-up" size={24} color="#F59E0B" />
            <Text style={styles.statNumber}>{completionRate}%</Text>
            <Text style={styles.statLabel}>Progress</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Goals</Text>
          {recentItems.length > 0 ? (
            <View style={styles.recentGoals}>
              {recentItems.map((item) => (
                <Pressable
                  key={item.id}
                  style={styles.recentGoalItem}
                  onPress={() => router.push(`/goal/${item.id}`)}
                >
                  <View style={styles.recentGoalInfo}>
                    <Text style={styles.recentGoalTitle} numberOfLines={1}>
                      {item.title}
                    </Text>
                    <Text style={styles.recentGoalCategory}>
                      {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                    </Text>
                  </View>
                  <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status || 'not_started') }]} />
                </Pressable>
              ))}
              <Pressable
                style={styles.viewAllButton}
                onPress={() => router.push('/(tabs)/goals')}
              >
                <Text style={styles.viewAllText}>View All Goals</Text>
                <Ionicons name="chevron-forward" size={16} color="#667eea" />
              </Pressable>
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="calendar-outline" size={48} color="#9CA3AF" />
              <Text style={styles.emptyStateText}>No goals yet</Text>
              <Text style={styles.emptyStateSubtext}>
                Start by adding your first goal!
              </Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <Pressable
              style={styles.actionButton}
              onPress={() => router.push('/add-goal')}
            >
              <Ionicons name="add-circle" size={24} color="#667eea" />
              <Text style={styles.actionText}>Add Goal</Text>
            </Pressable>
            <Pressable
              style={styles.actionButton}
              onPress={() => router.push('/ai-suggestions')}
            >
              <Ionicons name="sparkles" size={24} color="#667eea" />
              <Text style={styles.actionText}>AI Suggestions</Text>
            </Pressable>
            <Pressable
              style={styles.actionButton}
              onPress={() => router.push('/(tabs)/progress')}
            >
              <Ionicons name="camera" size={24} color="#667eea" />
              <Text style={styles.actionText}>Track Progress</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: 'white',
  },
  greeting: {
    fontSize: 16,
    color: '#6B7280',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  profileButton: {
    padding: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  emptyState: {
    backgroundColor: 'white',
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginTop: 8,
  },
  recentGoals: {
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recentGoalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  recentGoalInfo: {
    flex: 1,
  },
  recentGoalTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  recentGoalCategory: {
    fontSize: 12,
    color: '#6B7280',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#667eea',
    marginRight: 4,
  },
})
