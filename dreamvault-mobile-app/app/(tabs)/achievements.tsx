import { useEffect, useState } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable, RefreshControl } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { achievementsApi } from '../../src/services/api'
import { Achievement } from '../../src/types/database'
import { useBucketListStore } from '../../src/stores/bucketListStore'

const achievementIcons: Record<string, string> = {
  first_goal: 'flag',
  goal_completed: 'checkmark-circle',
  streak: 'flame',
  category_master: 'star',
  social: 'people',
  milestone: 'trophy',
}

const achievementColors: Record<string, string> = {
  first_goal: '#10B981',
  goal_completed: '#3B82F6',
  streak: '#F59E0B',
  category_master: '#8B5CF6',
  social: '#EC4899',
  milestone: '#F59E0B',
}

export default function Achievements() {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const { getTotalCount, getCompletedCount } = useBucketListStore()

  useEffect(() => {
    loadAchievements()
  }, [])

  const loadAchievements = async () => {
    try {
      const data = await achievementsApi.getUserAchievements()
      setAchievements(data)
    } catch (error) {
      console.error('Failed to load achievements:', error)
    } finally {
      setLoading(false)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadAchievements()
    setRefreshing(false)
  }

  const totalGoals = getTotalCount()
  const completedGoals = getCompletedCount()
  const totalPoints = achievements.reduce((sum, achievement) => sum + (achievement.points || 0), 0)

  const renderAchievement = (achievement: Achievement) => {
    const iconName = achievementIcons[achievement.type] || 'trophy'
    const color = achievementColors[achievement.type] || '#667eea'

    return (
      <View key={achievement.id} style={styles.achievementCard}>
        <View style={[styles.achievementIcon, { backgroundColor: color }]}>
          <Ionicons name={iconName as any} size={24} color="white" />
        </View>

        <View style={styles.achievementContent}>
          <Text style={styles.achievementTitle}>{achievement.title}</Text>
          {achievement.description && (
            <Text style={styles.achievementDescription}>{achievement.description}</Text>
          )}
          <View style={styles.achievementMeta}>
            <Text style={styles.achievementPoints}>+{achievement.points || 0} points</Text>
            <Text style={styles.achievementDate}>
              {achievement.unlocked_at ? new Date(achievement.unlocked_at).toLocaleDateString() : ''}
            </Text>
          </View>
        </View>
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Achievements</Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.statsSection}>
          <View style={styles.statCard}>
            <Ionicons name="trophy" size={24} color="#F59E0B" />
            <Text style={styles.statNumber}>{achievements.length}</Text>
            <Text style={styles.statLabel}>Achievements</Text>
          </View>
          <View style={styles.statCard}>
            <Ionicons name="star" size={24} color="#667eea" />
            <Text style={styles.statNumber}>{totalPoints}</Text>
            <Text style={styles.statLabel}>Total Points</Text>
          </View>
          <View style={styles.statCard}>
            <Ionicons name="checkmark-circle" size={24} color="#10B981" />
            <Text style={styles.statNumber}>{completedGoals}</Text>
            <Text style={styles.statLabel}>Goals Done</Text>
          </View>
        </View>

        {achievements.length > 0 ? (
          <View style={styles.achievementsSection}>
            <Text style={styles.sectionTitle}>Your Achievements</Text>
            {achievements.map(renderAchievement)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="trophy-outline" size={64} color="#9CA3AF" />
            <Text style={styles.emptyStateText}>No achievements yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Complete goals and milestones to unlock achievements!
            </Text>
          </View>
        )}

        <View style={styles.upcomingSection}>
          <Text style={styles.sectionTitle}>Upcoming Achievements</Text>
          <View style={styles.upcomingCard}>
            <Ionicons name="flag-outline" size={20} color="#9CA3AF" />
            <Text style={styles.upcomingText}>First Goal - Create your first bucket list item</Text>
          </View>
          <View style={styles.upcomingCard}>
            <Ionicons name="checkmark-circle-outline" size={20} color="#9CA3AF" />
            <Text style={styles.upcomingText}>Goal Crusher - Complete your first goal</Text>
          </View>
          <View style={styles.upcomingCard}>
            <Ionicons name="flame-outline" size={20} color="#9CA3AF" />
            <Text style={styles.upcomingText}>On Fire - Complete 3 goals in a week</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsSection: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 20,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  achievementsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  achievementCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  achievementMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  achievementPoints: {
    fontSize: 12,
    fontWeight: '600',
    color: '#667eea',
  },
  achievementDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  upcomingSection: {
    marginBottom: 40,
  },
  upcomingCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    opacity: 0.7,
  },
  upcomingText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
})
