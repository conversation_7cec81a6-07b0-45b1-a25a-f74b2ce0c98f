import { useEffect } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { useBucketListStore } from '../../src/stores/bucketListStore'

export default function Progress() {
  const { items, fetchItems, getItemsByStatus, getTotalCount, getCompletedCount } = useBucketListStore()

  useEffect(() => {
    fetchItems()
  }, [])

  const inProgressItems = getItemsByStatus('IN_PROGRESS')
  const completedItems = getItemsByStatus('COMPLETED')
  const totalItems = getTotalCount()
  const completedCount = getCompletedCount()
  const completionRate = totalItems > 0 ? Math.round((completedCount / totalItems) * 100) : 0

  const recentlyCompleted = completedItems.slice(0, 3)

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Progress</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.statsSection}>
          <View style={styles.progressCard}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Overall Progress</Text>
              <Text style={styles.progressPercentage}>{completionRate}%</Text>
            </View>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${completionRate}%` }]} />
            </View>
            <Text style={styles.progressText}>
              {completedCount} of {totalItems} goals completed
            </Text>
          </View>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Ionicons name="play-circle" size={24} color="#3B82F6" />
              <Text style={styles.statNumber}>{inProgressItems.length}</Text>
              <Text style={styles.statLabel}>In Progress</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="checkmark-circle" size={24} color="#10B981" />
              <Text style={styles.statNumber}>{completedCount}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
          </View>
        </View>

        {inProgressItems.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Currently Working On</Text>
            {inProgressItems.slice(0, 3).map((item) => (
              <Pressable
                key={item.id}
                style={styles.goalItem}
                onPress={() => router.push(`/goal/${item.id}`)}
              >
                <View style={styles.goalInfo}>
                  <Text style={styles.goalTitle}>{item.title}</Text>
                  <Text style={styles.goalCategory}>
                    {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </Pressable>
            ))}
          </View>
        )}

        {recentlyCompleted.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recently Completed</Text>
            {recentlyCompleted.map((item) => (
              <Pressable
                key={item.id}
                style={styles.goalItem}
                onPress={() => router.push(`/goal/${item.id}`)}
              >
                <View style={styles.goalInfo}>
                  <Text style={styles.goalTitle}>{item.title}</Text>
                  <Text style={styles.completedDate}>
                    Completed {item.completed_date ? new Date(item.completed_date).toLocaleDateString() : 'Recently'}
                  </Text>
                </View>
                <Ionicons name="checkmark-circle" size={20} color="#10B981" />
              </Pressable>
            ))}
          </View>
        )}

        {totalItems === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="trending-up-outline" size={64} color="#9CA3AF" />
            <Text style={styles.emptyStateText}>No progress tracked</Text>
            <Text style={styles.emptyStateSubtext}>
              Start working on your goals to see your progress here!
            </Text>
            <Pressable
              style={styles.addGoalButton}
              onPress={() => router.push('/add-goal')}
            >
              <Text style={styles.addGoalButtonText}>Add Your First Goal</Text>
            </Pressable>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsSection: {
    paddingVertical: 20,
  },
  progressCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#667eea',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  goalCategory: {
    fontSize: 14,
    color: '#6B7280',
  },
  completedDate: {
    fontSize: 14,
    color: '#10B981',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  addGoalButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  addGoalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
})
