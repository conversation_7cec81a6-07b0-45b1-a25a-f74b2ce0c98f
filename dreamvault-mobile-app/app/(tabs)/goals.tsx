import { useEffect, useState } from 'react'
import { View, Text, StyleSheet, FlatList, Pressable, RefreshControl, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { useBucketListStore } from '../../src/stores/bucketListStore'
import { BucketListItem, BucketListStatus } from '../../src/types/database'

const statusColors = {
  PLAYING: '#6B7280',
  IN_PROGRESS: '#3B82F6',
  COMPLETED: '#10B981',
  PAUSED: '#F59E0B',
}

const statusIcons = {
  PLAYING: 'ellipse-outline',
  IN_PROGRESS: 'play-circle',
  COMPLETED: 'checkmark-circle',
  PAUSED: 'pause-circle',
}

const categoryIcons = {
  TRAVEL: 'airplane',
  CAREER: 'briefcase',
  PERSONAL: 'person',
  RELATIONSHIPS: 'heart',
  ADVENTURES: 'compass',
  LEARNING: 'book',
  HEALTH: 'fitness',
  CREATIVE: 'brush',
  FINANCIAL: 'card',
  SPIRITUAL: 'leaf',
}

export default function Goals() {
  const { items, loading, error, fetchItems, deleteItem, clearError } = useBucketListStore()
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchItems()
  }, [])

  const onRefresh = async () => {
    setRefreshing(true)
    await fetchItems()
    setRefreshing(false)
  }

  const handleDeleteItem = (item: BucketListItem) => {
    Alert.alert(
      'Delete Goal',
      `Are you sure you want to delete "${item.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteItem(item.id)
        },
      ]
    )
  }

  const renderGoalItem = ({ item }: { item: BucketListItem }) => (
    <Pressable
      style={styles.goalItem}
      onPress={() => router.push(`/goal/${item.id}`)}
    >
      <View style={styles.goalHeader}>
        <View style={styles.goalInfo}>
          <View style={styles.categoryContainer}>
            <Ionicons
              name={categoryIcons[item.category] as any}
              size={16}
              color="#667eea"
            />
            <Text style={styles.categoryText}>
              {item.category.charAt(0) + item.category.slice(1).toLowerCase()}
            </Text>
          </View>
          <Text style={styles.goalTitle}>{item.title}</Text>
          {item.description && (
            <Text style={styles.goalDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
        </View>
        <Pressable
          style={styles.deleteButton}
          onPress={() => handleDeleteItem(item)}
        >
          <Ionicons name="trash-outline" size={20} color="#EF4444" />
        </Pressable>
      </View>

      <View style={styles.goalFooter}>
        <View style={styles.statusContainer}>
          <Ionicons
            name={statusIcons[item.status || 'not_started'] as any}
            size={16}
            color={statusColors[item.status || 'not_started']}
          />
          <Text style={[styles.statusText, { color: statusColors[item.status || 'not_started'] }]}>
            {(item.status || 'not_started').replace('_', ' ').toUpperCase()}
          </Text>
        </View>
        {item.target_date && (
          <Text style={styles.targetDate}>
            Target: {new Date(item.target_date).toLocaleDateString()}
          </Text>
        )}
      </View>
    </Pressable>
  )

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <Text style={styles.errorText}>{error}</Text>
          <Pressable style={styles.retryButton} onPress={() => { clearError(); fetchItems(); }}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </Pressable>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Goals</Text>
        <Pressable
          style={styles.addButton}
          onPress={() => router.push('/add-goal')}
        >
          <Ionicons name="add" size={24} color="white" />
        </Pressable>
      </View>

      {items.length === 0 && !loading ? (
        <View style={styles.emptyState}>
          <Ionicons name="list-outline" size={64} color="#9CA3AF" />
          <Text style={styles.emptyStateText}>No goals yet</Text>
          <Text style={styles.emptyStateSubtext}>
            Start building your bucket list by adding your first goal!
          </Text>
          <Pressable
            style={styles.addFirstGoalButton}
            onPress={() => router.push('/add-goal')}
          >
            <Text style={styles.addFirstGoalButtonText}>Add Your First Goal</Text>
          </Pressable>
        </View>
      ) : (
        <FlatList
          data={items}
          renderItem={renderGoalItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  addButton: {
    backgroundColor: '#667eea',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
  },
  goalItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  goalInfo: {
    flex: 1,
    marginRight: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '600',
    marginLeft: 6,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  goalDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  deleteButton: {
    padding: 4,
  },
  goalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  targetDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  addFirstGoalButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  addFirstGoalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
})
