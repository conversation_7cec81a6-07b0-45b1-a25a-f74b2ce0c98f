import { useState } from 'react'
import { View, Text, StyleSheet, TextInput, Pressable, ScrollView, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router, useLocalSearchParams } from 'expo-router'
import { progressApi } from '../src/services/api'
import CameraModal from '../src/components/CameraModal'

export default function AddProgress() {
  const { goalId } = useLocalSearchParams<{ goalId: string }>()
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [progressPercentage, setProgressPercentage] = useState('0')
  const [loading, setLoading] = useState(false)
  const [showCameraModal, setShowCameraModal] = useState(false)

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a progress title')
      return
    }

    if (!goalId) {
      Alert.alert('Error', 'Goal ID is missing')
      return
    }

    setLoading(true)
    
    try {
      await progressApi.createProgressEntry({
        bucket_list_item_id: goalId,
        title: title.trim(),
        description: description.trim() || undefined,
        progress_percentage: parseInt(progressPercentage) || 0,
      })

      Alert.alert(
        'Success',
        'Progress entry added successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      )
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add progress entry')
    } finally {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.title}>Add Progress</Text>
        <Pressable 
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.label}>Progress Title *</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="What progress did you make?"
            multiline
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Describe your progress in detail..."
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Progress Percentage</Text>
          <View style={styles.percentageContainer}>
            <TextInput
              style={styles.percentageInput}
              value={progressPercentage}
              onChangeText={setProgressPercentage}
              placeholder="0"
              keyboardType="numeric"
              maxLength={3}
            />
            <Text style={styles.percentageSymbol}>%</Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${Math.min(parseInt(progressPercentage) || 0, 100)}%` }
              ]}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Add Photos</Text>
          <Pressable
            style={styles.photoButton}
            onPress={() => setShowCameraModal(true)}
          >
            <Ionicons name="camera-outline" size={24} color="#667eea" />
            <Text style={styles.photoButtonText}>Take Photo</Text>
          </Pressable>
        </View>
      </ScrollView>

      <CameraModal
        visible={showCameraModal}
        onClose={() => setShowCameraModal(false)}
        bucketListItemId={goalId || ''}
        onMediaUploaded={() => {
          // Refresh media or show success message
          console.log('Media uploaded successfully')
        }}
      />
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  saveButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  percentageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  percentageInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  percentageSymbol: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 16,
  },
  photoButtonText: {
    fontSize: 16,
    color: '#667eea',
    marginLeft: 8,
    fontWeight: '600',
  },
})
