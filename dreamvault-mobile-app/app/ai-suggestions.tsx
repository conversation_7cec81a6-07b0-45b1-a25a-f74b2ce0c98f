import { useState, useEffect } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, ActivityIndicator } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { aiService } from '../src/services/aiService'
import { useBucketListStore } from '../src/stores/bucketListStore'

interface SmartSuggestion {
  title: string
  description: string
  category: string
  reasoning: string
  confidence: number
}

export default function AISuggestionsScreen() {
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([])
  const [loading, setLoading] = useState(true)
  const [aiStatus, setAiStatus] = useState<{ isAvailable: boolean; features: string[] }>({
    isAvailable: false,
    features: []
  })
  const { items, createItem } = useBucketListStore()

  useEffect(() => {
    checkAIStatus()
    loadSuggestions()
  }, [])

  const checkAIStatus = async () => {
    try {
      const status = await aiService.checkStatus()
      setAiStatus(status)
    } catch (error) {
      console.error('Failed to check AI status:', error)
    }
  }

  const loadSuggestions = async () => {
    setLoading(true)
    try {
      const userGoals = items.map(item => `${item.title}: ${item.description || ''}`).slice(0, 10)
      const result = await aiService.getSmartSuggestions(userGoals)
      setSuggestions(result.suggestions)
    } catch (error) {
      console.error('Failed to load suggestions:', error)
      Alert.alert('Error', 'Failed to load AI suggestions. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateGoal = async (suggestion: SmartSuggestion) => {
    try {
      const result = await createItem({
        title: suggestion.title,
        description: suggestion.description,
        category: suggestion.category as any,
        priority: 'MEDIUM',
        status: 'PLAYING',
        is_public: false,
      })

      if (result) {
        Alert.alert(
          'Success',
          'Goal created from AI suggestion!',
          [{ text: 'OK', onPress: () => router.back() }]
        )
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to create goal')
    }
  }

  const renderSuggestion = (suggestion: SmartSuggestion, index: number) => (
    <View key={index} style={styles.suggestionCard}>
      <View style={styles.suggestionHeader}>
        <View style={styles.categoryBadge}>
          <Text style={styles.categoryText}>{suggestion.category}</Text>
        </View>
        <View style={styles.confidenceContainer}>
          <Ionicons 
            name="sparkles" 
            size={12} 
            color={suggestion.confidence > 0.7 ? '#10B981' : '#F59E0B'} 
          />
          <Text style={styles.confidenceText}>
            {Math.round(suggestion.confidence * 100)}%
          </Text>
        </View>
      </View>

      <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
      <Text style={styles.suggestionDescription}>{suggestion.description}</Text>
      
      <View style={styles.reasoningContainer}>
        <Text style={styles.reasoningLabel}>Why this suggestion:</Text>
        <Text style={styles.reasoningText}>{suggestion.reasoning}</Text>
      </View>

      <View style={styles.suggestionActions}>
        <Pressable 
          style={styles.createButton}
          onPress={() => handleCreateGoal(suggestion)}
        >
          <Ionicons name="add" size={16} color="white" />
          <Text style={styles.createButtonText}>Create Goal</Text>
        </Pressable>
        
        <Pressable 
          style={styles.customizeButton}
          onPress={() => router.push({
            pathname: '/add-goal',
            params: {
              title: suggestion.title,
              description: suggestion.description,
              category: suggestion.category
            }
          })}
        >
          <Ionicons name="create-outline" size={16} color="#667eea" />
          <Text style={styles.customizeButtonText}>Customize</Text>
        </Pressable>
      </View>
    </View>
  )

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.title}>AI Suggestions</Text>
        <Pressable style={styles.refreshButton} onPress={loadSuggestions}>
          <Ionicons name="refresh" size={24} color="#667eea" />
        </Pressable>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Ionicons 
              name={aiStatus.isAvailable ? "checkmark-circle" : "alert-circle"} 
              size={20} 
              color={aiStatus.isAvailable ? "#10B981" : "#F59E0B"} 
            />
            <Text style={styles.statusTitle}>
              AI Service {aiStatus.isAvailable ? 'Available' : 'Unavailable'}
            </Text>
          </View>
          {aiStatus.features.length > 0 && (
            <Text style={styles.featuresText}>
              Features: {aiStatus.features.join(', ')}
            </Text>
          )}
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.loadingText}>Generating personalized suggestions...</Text>
          </View>
        ) : suggestions.length > 0 ? (
          <View style={styles.suggestionsContainer}>
            <Text style={styles.sectionTitle}>
              Personalized Goal Suggestions
            </Text>
            {suggestions.map(renderSuggestion)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="bulb-outline" size={64} color="#9CA3AF" />
            <Text style={styles.emptyStateText}>No suggestions available</Text>
            <Text style={styles.emptyStateSubtext}>
              Add more goals to get personalized AI suggestions!
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  refreshButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    marginBottom: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  featuresText: {
    fontSize: 14,
    color: '#6B7280',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 16,
  },
  suggestionsContainer: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  suggestionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  suggestionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryBadge: {
    backgroundColor: '#667eea',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  confidenceText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '600',
  },
  suggestionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  suggestionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  reasoningContainer: {
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  reasoningLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  reasoningText: {
    fontSize: 12,
    color: '#6B7280',
    lineHeight: 16,
  },
  suggestionActions: {
    flexDirection: 'row',
    gap: 12,
  },
  createButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 12,
    gap: 6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  customizeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 12,
    gap: 6,
  },
  customizeButtonText: {
    color: '#667eea',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 8,
    textAlign: 'center',
  },
})
