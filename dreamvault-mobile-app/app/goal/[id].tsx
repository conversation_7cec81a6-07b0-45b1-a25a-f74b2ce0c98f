import { useEffect, useState } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, Image } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router, useLocalSearchParams } from 'expo-router'
import { bucketListApi } from '../../src/services/api'
import { BucketListItem, BucketListStatus, MediaFile } from '../../src/types/database'
import { useBucketListStore } from '../../src/stores/bucketListStore'
import { mediaService } from '../../src/services/mediaService'
import CameraModal from '../../src/components/CameraModal'

const statusOptions: { value: BucketListStatus; label: string; color: string; icon: string }[] = [
  { value: 'PLAYING', label: 'Playing', color: '#6B7280', icon: 'ellipse-outline' },
  { value: 'IN_PROGRESS', label: 'In Progress', color: '#3B82F6', icon: 'play-circle' },
  { value: 'COMPLETED', label: 'Completed', color: '#10B981', icon: 'checkmark-circle' },
  { value: 'PAUSED', label: 'Paused', color: '#F59E0B', icon: 'pause-circle' },
]

export default function GoalDetail() {
  const { id } = useLocalSearchParams<{ id: string }>()
  const [goal, setGoal] = useState<BucketListItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [showCameraModal, setShowCameraModal] = useState(false)
  const { updateItem } = useBucketListStore()

  useEffect(() => {
    if (id) {
      loadGoal()
      loadMediaFiles()
    }
  }, [id])

  const loadGoal = async () => {
    try {
      const goalData = await bucketListApi.getItem(id!)
      setGoal(goalData)
    } catch (error) {
      Alert.alert('Error', 'Failed to load goal details')
      router.back()
    } finally {
      setLoading(false)
    }
  }

  const loadMediaFiles = async () => {
    try {
      const files = await mediaService.getMediaFiles(id!)
      setMediaFiles(files)
    } catch (error) {
      console.error('Failed to load media files:', error)
    }
  }

  const updateStatus = async (newStatus: BucketListStatus) => {
    if (!goal) return

    const updates: Partial<BucketListItem> = { status: newStatus }
    if (newStatus === 'COMPLETED' && !goal.completed_date) {
      updates.completed_date = new Date().toISOString().split('T')[0]
    }

    const updatedGoal = await updateItem(goal.id, updates)
    if (updatedGoal) {
      setGoal(updatedGoal)
    }
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    )
  }

  if (!goal) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Goal not found</Text>
        </View>
      </SafeAreaView>
    )
  }

  const currentStatus = statusOptions.find(s => s.value === goal.status) || statusOptions[0]

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.headerTitle}>Goal Details</Text>
        <Pressable style={styles.editButton}>
          <Ionicons name="create-outline" size={24} color="#667eea" />
        </Pressable>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.goalHeader}>
          <Text style={styles.goalTitle}>{goal.title}</Text>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>
              {goal.category.charAt(0).toUpperCase() + goal.category.slice(1)}
            </Text>
          </View>
        </View>

        {goal.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{goal.description}</Text>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          <View style={styles.statusContainer}>
            {statusOptions.map((status) => (
              <Pressable
                key={status.value}
                style={[
                  styles.statusOption,
                  goal.status === status.value && { backgroundColor: status.color }
                ]}
                onPress={() => updateStatus(status.value)}
              >
                <Ionicons 
                  name={status.icon as any} 
                  size={20} 
                  color={goal.status === status.value ? 'white' : status.color} 
                />
                <Text style={[
                  styles.statusText,
                  goal.status === status.value && { color: 'white' }
                ]}>
                  {status.label}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>

        <View style={styles.detailsGrid}>
          {goal.location && (
            <View style={styles.detailItem}>
              <Ionicons name="location-outline" size={20} color="#6B7280" />
              <Text style={styles.detailText}>{goal.location}</Text>
            </View>
          )}
          
          {goal.target_date && (
            <View style={styles.detailItem}>
              <Ionicons name="calendar-outline" size={20} color="#6B7280" />
              <Text style={styles.detailText}>
                Target: {new Date(goal.target_date).toLocaleDateString()}
              </Text>
            </View>
          )}

          {goal.estimated_cost && (
            <View style={styles.detailItem}>
              <Ionicons name="card-outline" size={20} color="#6B7280" />
              <Text style={styles.detailText}>
                Budget: ${goal.estimated_cost.toFixed(2)}
              </Text>
            </View>
          )}

          {goal.completed_date && (
            <View style={styles.detailItem}>
              <Ionicons name="checkmark-circle-outline" size={20} color="#10B981" />
              <Text style={styles.detailText}>
                Completed: {new Date(goal.completed_date).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>

        {mediaFiles.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Photos</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.mediaGallery}>
                {mediaFiles.map((file) => (
                  <View key={file.id} style={styles.mediaItem}>
                    <Image source={{ uri: file.file_path }} style={styles.mediaImage} />
                    {file.caption && (
                      <Text style={styles.mediaCaption} numberOfLines={2}>
                        {file.caption}
                      </Text>
                    )}
                  </View>
                ))}
              </View>
            </ScrollView>
          </View>
        )}

        <View style={styles.actionButtons}>
          <Pressable
            style={styles.actionButton}
            onPress={() => setShowCameraModal(true)}
          >
            <Ionicons name="camera-outline" size={20} color="#667eea" />
            <Text style={styles.actionButtonText}>Add Photo</Text>
          </Pressable>

          <Pressable
            style={styles.actionButton}
            onPress={() => router.push(`/add-progress?goalId=${goal.id}`)}
          >
            <Ionicons name="trending-up-outline" size={20} color="#667eea" />
            <Text style={styles.actionButtonText}>Add Progress</Text>
          </Pressable>

          <Pressable
            style={styles.actionButton}
            onPress={() => router.push(`/milestones/${goal.id}`)}
          >
            <Ionicons name="flag-outline" size={20} color="#667eea" />
            <Text style={styles.actionButtonText}>Milestones</Text>
          </Pressable>
        </View>
      </ScrollView>

      <CameraModal
        visible={showCameraModal}
        onClose={() => setShowCameraModal(false)}
        bucketListItemId={goal?.id || ''}
        onMediaUploaded={loadMediaFiles}
      />
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  editButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  goalHeader: {
    paddingVertical: 24,
    alignItems: 'center',
  },
  goalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  categoryBadge: {
    backgroundColor: '#667eea',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  categoryText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: '45%',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  detailsGrid: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 12,
  },
  mediaGallery: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  mediaItem: {
    width: 120,
  },
  mediaImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  mediaCaption: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#667eea',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
