import { useState } from 'react'
import { View, Text, StyleSheet, TextInput, Pressable, ScrollView, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { useBucketListStore } from '../src/stores/bucketListStore'
import { BucketListCategory, BucketListPriority } from '../src/types/database'
import AISuggestions from '../src/components/AISuggestions'

const categories: { value: BucketListCategory; label: string; icon: string }[] = [
  { value: 'TRAVEL', label: 'Travel', icon: 'airplane' },
  { value: 'CAREER', label: 'Career', icon: 'briefcase' },
  { value: 'PERSONAL', label: 'Personal', icon: 'person' },
  { value: 'RELATIONSHIPS', label: 'Relationships', icon: 'heart' },
  { value: 'ADVENTURES', label: 'Adventures', icon: 'compass' },
  { value: 'LEARNING', label: 'Learning', icon: 'book' },
  { value: 'HEALTH', label: 'Health', icon: 'fitness' },
  { value: 'CREATIVE', label: 'Creative', icon: 'brush' },
  { value: 'FINANCIAL', label: 'Financial', icon: 'card' },
  { value: 'SPIRITUAL', label: 'Spiritual', icon: 'leaf' },
]

const priorities: { value: BucketListPriority; label: string; color: string }[] = [
  { value: 'LOW', label: 'Low', color: '#6B7280' },
  { value: 'MEDIUM', label: 'Medium', color: '#F59E0B' },
  { value: 'HIGH', label: 'High', color: '#EF4444' },
]

export default function AddGoal() {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [category, setCategory] = useState<BucketListCategory>('PERSONAL')
  const [priority, setPriority] = useState<BucketListPriority>('MEDIUM')
  const [location, setLocation] = useState('')
  const [targetDate, setTargetDate] = useState('')
  const [estimatedCost, setEstimatedCost] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const { createItem } = useBucketListStore()

  const handleCategorySelect = (suggestedCategory: BucketListCategory) => {
    setCategory(suggestedCategory)
  }

  const handleTagSelect = (suggestedTags: string[]) => {
    const newTags = [...new Set([...tags, ...suggestedTags])]
    setTags(newTags)
  }

  const handleLocationSelect = (suggestedLocation: string) => {
    setLocation(suggestedLocation)
  }

  const handleSave = async () => {
    console.log('💾 [AddGoal] Starting goal creation process')

    if (!title.trim()) {
      console.warn('⚠️ [AddGoal] Validation failed: empty title')
      Alert.alert('Error', 'Please enter a goal title')
      return
    }

    setLoading(true)

    const goalData = {
      title: title.trim(),
      description: description.trim() || undefined,
      category,
      priority,
      location: location.trim() || undefined,
      target_date: targetDate || undefined,
      estimated_cost: estimatedCost ? parseFloat(estimatedCost) : undefined,
      tags: tags.length > 0 ? tags : undefined,
      status: 'PLAYING' as const,
      is_public: false,
    }

    console.log('📋 [AddGoal] Goal data prepared:', JSON.stringify(goalData, null, 2))

    try {
      const result = await createItem(goalData)
      setLoading(false)

      if (result) {
        console.log('✅ [AddGoal] Goal created successfully:', result.id)
        Alert.alert(
          'Success',
          'Goal created successfully!',
          [{ text: 'OK', onPress: () => router.back() }]
        )
      } else {
        console.error('❌ [AddGoal] Create item returned null')
        Alert.alert('Error', 'Failed to create goal. Please try again.')
      }
    } catch (error: any) {
      console.error('❌ [AddGoal] Unexpected error:', error)
      setLoading(false)
      Alert.alert('Error', `Failed to create goal: ${error.message}`)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.title}>Add New Goal</Text>
        <Pressable 
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.label}>Goal Title *</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="What do you want to achieve?"
            multiline
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Describe your goal in detail..."
            multiline
            numberOfLines={4}
          />
        </View>

        {(title.trim().length > 3 || description.trim().length > 10) && (
          <AISuggestions
            title={title}
            description={description}
            onCategorySelect={handleCategorySelect}
            onTagSelect={handleTagSelect}
            onLocationSelect={handleLocationSelect}
          />
        )}

        <View style={styles.section}>
          <Text style={styles.label}>Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
            {categories.map((cat) => (
              <Pressable
                key={cat.value}
                style={[
                  styles.categoryItem,
                  category === cat.value && styles.categoryItemSelected
                ]}
                onPress={() => setCategory(cat.value)}
              >
                <Ionicons 
                  name={cat.icon as any} 
                  size={20} 
                  color={category === cat.value ? 'white' : '#667eea'} 
                />
                <Text style={[
                  styles.categoryText,
                  category === cat.value && styles.categoryTextSelected
                ]}>
                  {cat.label}
                </Text>
              </Pressable>
            ))}
          </ScrollView>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Priority</Text>
          <View style={styles.priorityContainer}>
            {priorities.map((pri) => (
              <Pressable
                key={pri.value}
                style={[
                  styles.priorityItem,
                  priority === pri.value && { backgroundColor: pri.color }
                ]}
                onPress={() => setPriority(pri.value)}
              >
                <Text style={[
                  styles.priorityText,
                  priority === pri.value && styles.priorityTextSelected
                ]}>
                  {pri.label}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Location (Optional)</Text>
          <TextInput
            style={styles.input}
            value={location}
            onChangeText={setLocation}
            placeholder="Where will you achieve this goal?"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Target Date (Optional)</Text>
          <TextInput
            style={styles.input}
            value={targetDate}
            onChangeText={setTargetDate}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Estimated Cost (Optional)</Text>
          <TextInput
            style={styles.input}
            value={estimatedCost}
            onChangeText={setEstimatedCost}
            placeholder="0.00"
            keyboardType="numeric"
          />
        </View>

        {tags.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.label}>Tags</Text>
            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.tagChip}>
                  <Text style={styles.tagText}>{tag}</Text>
                  <Pressable
                    style={styles.removeTagButton}
                    onPress={() => setTags(tags.filter((_, i) => i !== index))}
                  >
                    <Ionicons name="close" size={14} color="#6B7280" />
                  </Pressable>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  saveButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  categoryScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  categoryItem: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    minWidth: 80,
  },
  categoryItemSelected: {
    backgroundColor: '#667eea',
  },
  categoryText: {
    fontSize: 12,
    color: '#667eea',
    marginTop: 4,
    fontWeight: '600',
  },
  categoryTextSelected: {
    color: 'white',
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityItem: {
    flex: 1,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  priorityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  priorityTextSelected: {
    color: 'white',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
    color: '#374151',
  },
  removeTagButton: {
    padding: 2,
  },
})
