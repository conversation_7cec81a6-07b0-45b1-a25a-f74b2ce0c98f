import { useEffect, useState } from 'react'
import { View, Text, StyleSheet, FlatList, Pressable, Alert, TextInput } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router, useLocalSearchParams } from 'expo-router'
import { milestonesApi } from '../../src/services/api'
import { Milestone } from '../../src/types/database'

export default function MilestonesScreen() {
  const { goalId } = useLocalSearchParams<{ goalId: string }>()
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newMilestoneTitle, setNewMilestoneTitle] = useState('')

  useEffect(() => {
    if (goalId) {
      loadMilestones()
    }
  }, [goalId])

  const loadMilestones = async () => {
    try {
      const data = await milestonesApi.getMilestones(goalId!)
      setMilestones(data)
    } catch (error) {
      Alert.alert('Error', 'Failed to load milestones')
    } finally {
      setLoading(false)
    }
  }

  const addMilestone = async () => {
    if (!newMilestoneTitle.trim()) {
      Alert.alert('Error', 'Please enter a milestone title')
      return
    }

    try {
      const newMilestone = await milestonesApi.createMilestone({
        bucket_list_item_id: goalId!,
        title: newMilestoneTitle.trim(),
        is_completed: false,
        order_index: milestones.length,
      })

      setMilestones([...milestones, newMilestone])
      setNewMilestoneTitle('')
      setShowAddForm(false)
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add milestone')
    }
  }

  const toggleMilestone = async (milestone: Milestone) => {
    try {
      const updatedMilestone = await milestonesApi.updateMilestone(milestone.id, {
        is_completed: !milestone.is_completed,
        completed_date: !milestone.is_completed ? new Date().toISOString().split('T')[0] : undefined,
      })

      setMilestones(milestones.map(m => 
        m.id === milestone.id ? updatedMilestone : m
      ))
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update milestone')
    }
  }

  const renderMilestone = ({ item }: { item: Milestone }) => (
    <Pressable 
      style={styles.milestoneItem}
      onPress={() => toggleMilestone(item)}
    >
      <Ionicons 
        name={item.is_completed ? 'checkmark-circle' : 'ellipse-outline'} 
        size={24} 
        color={item.is_completed ? '#10B981' : '#9CA3AF'} 
      />
      <View style={styles.milestoneContent}>
        <Text style={[
          styles.milestoneTitle,
          item.is_completed && styles.completedTitle
        ]}>
          {item.title}
        </Text>
        {item.completed_date && (
          <Text style={styles.completedDate}>
            Completed {new Date(item.completed_date).toLocaleDateString()}
          </Text>
        )}
      </View>
    </Pressable>
  )

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.title}>Milestones</Text>
        <Pressable 
          style={styles.addButton}
          onPress={() => setShowAddForm(true)}
        >
          <Ionicons name="add" size={24} color="#667eea" />
        </Pressable>
      </View>

      {showAddForm && (
        <View style={styles.addForm}>
          <TextInput
            style={styles.addInput}
            value={newMilestoneTitle}
            onChangeText={setNewMilestoneTitle}
            placeholder="Enter milestone title..."
            autoFocus
          />
          <View style={styles.addFormButtons}>
            <Pressable 
              style={styles.cancelButton}
              onPress={() => {
                setShowAddForm(false)
                setNewMilestoneTitle('')
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </Pressable>
            <Pressable style={styles.saveButton} onPress={addMilestone}>
              <Text style={styles.saveButtonText}>Add</Text>
            </Pressable>
          </View>
        </View>
      )}

      <View style={styles.content}>
        {milestones.length === 0 && !loading ? (
          <View style={styles.emptyState}>
            <Ionicons name="flag-outline" size={64} color="#9CA3AF" />
            <Text style={styles.emptyStateText}>No milestones yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Break down your goal into smaller milestones to track progress!
            </Text>
            <Pressable
              style={styles.addFirstButton}
              onPress={() => setShowAddForm(true)}
            >
              <Text style={styles.addFirstButtonText}>Add First Milestone</Text>
            </Pressable>
          </View>
        ) : (
          <FlatList
            data={milestones}
            renderItem={renderMilestone}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  addButton: {
    padding: 4,
  },
  addForm: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  addInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 12,
  },
  addFormButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: 20,
  },
  milestoneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  milestoneContent: {
    flex: 1,
    marginLeft: 12,
  },
  milestoneTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#9CA3AF',
  },
  completedDate: {
    fontSize: 14,
    color: '#10B981',
    marginTop: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  addFirstButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  addFirstButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
})
