import { useState, useEffect } from 'react'
import { View, Text, StyleSheet, ScrollView, Pressable, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { supabase } from '../src/config/supabase'
import { bucketListApi, userApi, achievementsApi } from '../src/services/api'
import { useBucketListStore } from '../src/stores/bucketListStore'
import { useAuthStore } from '../src/stores/authStore'

export default function DatabaseTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [testing, setTesting] = useState(false)
  const { user } = useAuthStore()
  const { items, fetchItems } = useBucketListStore()

  const addResult = (message: string, success: boolean = true) => {
    const icon = success ? '✅' : '❌'
    setTestResults(prev => [...prev, `${icon} ${message}`])
  }

  const runDatabaseTests = async () => {
    setTesting(true)
    setTestResults([])

    try {
      addResult('Starting database connectivity tests...')

      // Test 1: Authentication
      addResult('Testing authentication...')
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        addResult(`User authenticated: ${authUser.email}`)
      } else {
        addResult('No authenticated user found', false)
        return
      }

      // Test 2: User profile
      addResult('Testing user profile retrieval...')
      try {
        const userProfile = await userApi.getCurrentUser()
        addResult(`User profile loaded: ${userProfile.full_name || userProfile.email}`)
      } catch (error: any) {
        addResult(`User profile error: ${error.message}`, false)
      }

      // Test 3: Bucket list items read
      addResult('Testing bucket list items read...')
      try {
        const bucketItems = await bucketListApi.getItems()
        addResult(`Found ${bucketItems.length} bucket list items`)
      } catch (error: any) {
        addResult(`Bucket list read error: ${error.message}`, false)
      }

      // Test 4: Create test bucket list item
      addResult('Testing bucket list item creation...')
      try {
        const testItem = await bucketListApi.createItem({
          title: 'Test Goal - Mobile App',
          description: 'This is a test goal created from the mobile app',
          category: 'PERSONAL',
          priority: 'MEDIUM',
          status: 'PLAYING',
          is_public: false,
        })
        addResult(`Created test item: ${testItem.title}`)

        // Test 5: Update the test item
        addResult('Testing bucket list item update...')
        const updatedItem = await bucketListApi.updateItem(testItem.id, {
          status: 'IN_PROGRESS',
          description: 'Updated from mobile app test'
        })
        addResult(`Updated item status to: ${updatedItem.status}`)

        // Test 6: Delete the test item
        addResult('Testing bucket list item deletion...')
        await bucketListApi.deleteItem(testItem.id)
        addResult('Test item deleted successfully')

      } catch (error: any) {
        addResult(`CRUD operations error: ${error.message}`, false)
      }

      // Test 7: Real-time subscription test
      addResult('Testing real-time subscriptions...')
      const subscription = supabase
        .channel('bucket_list_changes')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'bucket_list_items' },
          (payload) => {
            addResult(`Real-time event received: ${payload.eventType}`)
          }
        )
        .subscribe()

      // Clean up subscription after 2 seconds
      setTimeout(() => {
        subscription.unsubscribe()
        addResult('Real-time subscription test completed')
      }, 2000)

      // Test 8: Achievements
      addResult('Testing achievements retrieval...')
      try {
        const achievements = await achievementsApi.getUserAchievements()
        addResult(`Found ${achievements.length} achievements`)
      } catch (error: any) {
        addResult(`Achievements error: ${error.message}`, false)
      }

      // Test 9: Storage connectivity
      addResult('Testing Supabase Storage connectivity...')
      try {
        const { data: buckets } = await supabase.storage.listBuckets()
        addResult(`Storage accessible, found ${buckets?.length || 0} buckets`)
      } catch (error: any) {
        addResult(`Storage error: ${error.message}`, false)
      }

      addResult('🎉 Database tests completed!')

    } catch (error: any) {
      addResult(`Test suite error: ${error.message}`, false)
    } finally {
      setTesting(false)
    }
  }

  const syncWithWebApp = async () => {
    addResult('Syncing with web app data...')
    try {
      await fetchItems()
      addResult(`Synced ${items.length} items from database`)
    } catch (error: any) {
      addResult(`Sync error: ${error.message}`, false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </Pressable>
        <Text style={styles.title}>Database Test</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>Database Connectivity Test</Text>
          <Text style={styles.infoText}>
            This screen tests the connection between the mobile app and Supabase database,
            ensuring perfect sync with the web application.
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <Pressable 
            style={[styles.testButton, testing && styles.testButtonDisabled]}
            onPress={runDatabaseTests}
            disabled={testing}
          >
            <Ionicons name="play-circle" size={20} color="white" />
            <Text style={styles.testButtonText}>
              {testing ? 'Running Tests...' : 'Run Database Tests'}
            </Text>
          </Pressable>

          <Pressable style={styles.syncButton} onPress={syncWithWebApp}>
            <Ionicons name="sync" size={20} color="#667eea" />
            <Text style={styles.syncButtonText}>Sync with Web App</Text>
          </Pressable>
        </View>

        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Test Results:</Text>
          {testResults.map((result, index) => (
            <Text key={index} style={styles.resultText}>
              {result}
            </Text>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 24,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 16,
    gap: 8,
  },
  testButtonDisabled: {
    opacity: 0.6,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 16,
    gap: 8,
  },
  syncButtonText: {
    color: '#667eea',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 40,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  resultText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
})
