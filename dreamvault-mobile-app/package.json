{"name": "mobile-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.2.1", "@react-native-async-storage/async-storage": "2.1.2", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.84.1", "expo": "~53.0.20", "expo-camera": "^16.1.11", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.4", "expo-router": "^5.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}