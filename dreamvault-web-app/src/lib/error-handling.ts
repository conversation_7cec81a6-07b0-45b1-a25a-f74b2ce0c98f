import * as Sentry from '@sentry/nextjs'

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  extra?: Record<string, any>
}

export interface ApiError {
  code: string
  message: string
  details?: any
  timestamp: string
  requestId?: string
}

// Enhanced error handling utility
export class <PERSON>rrorHandler {
  static captureException(error: Error, context?: ErrorContext) {
    console.error('Error captured:', error, context)
    
    Sentry.withScope((scope) => {
      if (context?.component) {
        scope.setTag('component', context.component)
      }
      
      if (context?.action) {
        scope.setTag('action', context.action)
      }
      
      if (context?.userId) {
        scope.setUser({ id: context.userId })
      }
      
      if (context?.extra) {
        scope.setContext('extra', context.extra)
      }
      
      scope.setLevel('error')
      Sentry.captureException(error)
    })
  }

  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: ErrorContext) {
    console.log(`[${level.toUpperCase()}] ${message}`, context)
    
    Sentry.withScope((scope) => {
      if (context?.component) {
        scope.setTag('component', context.component)
      }
      
      if (context?.action) {
        scope.setTag('action', context.action)
      }
      
      if (context?.extra) {
        scope.setContext('extra', context.extra)
      }
      
      Sentry.captureMessage(message, level)
    })
  }

  static handleFetchError(error: Error, response?: Response, context?: ErrorContext): ApiError {
    const apiError: ApiError = {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString()
    }

    if (response) {
      if (response.status >= 400 && response.status < 500) {
        apiError.code = 'CLIENT_ERROR'
        apiError.message = `Request failed with status ${response.status}`
      } else if (response.status >= 500) {
        apiError.code = 'SERVER_ERROR'
        apiError.message = 'Server error - please try again later'
      }
    } else {
      apiError.code = 'NETWORK_ERROR'
      apiError.message = 'Network error - please check your connection'
    }

    // Only capture server errors and network errors
    if (!response || response.status >= 500) {
      this.captureException(error, {
        ...context,
        extra: {
          apiError,
          status: response?.status,
          url: response?.url
        }
      })
    }

    return apiError
  }

  static handleAsyncError<T>(
    asyncFn: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> {
    return asyncFn().catch((error) => {
      this.captureException(error, context)
      return null
    })
  }
}

// User-friendly error messages
export const getErrorMessage = (error: ApiError | Error | string): string => {
  if (typeof error === 'string') {
    return error
  }

  if (error instanceof Error) {
    return error.message
  }

  // Handle API errors
  switch (error.code) {
    case 'VALIDATION_FAILED':
      return 'Please check your input and try again'
    case 'UNAUTHORIZED':
      return 'You need to log in to access this feature'
    case 'FORBIDDEN':
      return 'You don\'t have permission to perform this action'
    case 'NOT_FOUND':
      return 'The requested item could not be found'
    case 'NETWORK_ERROR':
      return 'Network error - please check your connection and try again'
    case 'DATABASE_ERROR':
      return 'A database error occurred. Please try again later'
    case 'RATE_LIMIT_EXCEEDED':
      return 'Too many requests. Please wait a moment and try again'
    default:
      return error.message || 'An unexpected error occurred'
  }
}

// React hook for error handling
export function useErrorHandler() {
  return {
    captureException: ErrorHandler.captureException,
    captureMessage: ErrorHandler.captureMessage,
    handleFetchError: ErrorHandler.handleFetchError,
    handleAsyncError: ErrorHandler.handleAsyncError,
    getErrorMessage
  }
}

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    ErrorHandler.captureException(
      event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
      { component: 'global', action: 'unhandled_rejection' }
    )
  })
}
