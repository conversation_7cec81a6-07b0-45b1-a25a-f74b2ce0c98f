import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { SearchFilters } from '@dreamvault/types'
import { BucketListItemApiResponse } from '@/types/api'

interface BucketListState {
  items: BucketListItemApiResponse[]
  loading: boolean
  error: string | null
  searchFilters: SearchFilters
  selectedItem: BucketListItemApiResponse | null

  // Actions
  setItems: (items: BucketListItemApiResponse[]) => void
  addItem: (item: BucketListItemApiResponse) => void
  updateItem: (id: string, updates: Partial<BucketListItemApiResponse>) => void
  deleteItem: (id: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSearchFilters: (filters: Partial<SearchFilters>) => void
  setSelectedItem: (item: BucketListItemApiResponse | null) => void
  clearFilters: () => void
}

const initialFilters: SearchFilters = {}

export const useBucketListStore = create<BucketListState>()(
  devtools(
    (set, _get) => ({
      items: [],
      loading: false,
      error: null,
      searchFilters: initialFilters,
      selectedItem: null,

      setItems: (items) => set({ items }),
      
      addItem: (item) => set((state) => ({ 
        items: [item, ...state.items] 
      })),
      
      updateItem: (id, updates) => set((state) => ({
        items: state.items.map(item => 
          item.id === id ? { ...item, ...updates } : item
        )
      })),
      
      deleteItem: (id) => set((state) => ({
        items: state.items.filter(item => item.id !== id)
      })),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),
      
      setSearchFilters: (filters) => set((state) => ({
        searchFilters: { ...state.searchFilters, ...filters }
      })),
      
      setSelectedItem: (item) => set({ selectedItem: item }),
      
      clearFilters: () => set({ searchFilters: initialFilters })
    }),
    {
      name: 'bucket-list-store'
    }
  )
)

interface UIState {
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  notifications: Notification[]
  
  // Actions
  setSidebarOpen: (open: boolean) => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  addNotification: (notification: Notification) => void
  removeNotification: (id: string) => void
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
}

export const useUIStore = create<UIState>()(
  devtools(
    (set) => ({
      sidebarOpen: false,
      theme: 'system',
      notifications: [],

      setSidebarOpen: (open) => set({ sidebarOpen: open }),

      setTheme: (theme) => set({ theme }),

      addNotification: (notification) => set((state) => ({
        notifications: [...state.notifications, notification]
      })),

      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      }))
    }),
    {
      name: 'ui-store'
    }
  )
)

// Social Features Store
interface DiscoveryItem {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  popularityScore: number
  shareCount: number
  completionCount: number
  isCompleted: boolean
  user: {
    id: string
    name: string
    picture?: string
  }
}

interface SharedList {
  id: string
  title: string
  description: string
  isPublic: boolean
  createdAt: string
  updatedAt: string
  owner: {
    id: string
    name: string
    picture?: string
  }
  members: Array<{
    id: string
    role: 'OWNER' | 'EDITOR' | 'VIEWER'
    user: {
      id: string
      name: string
      picture?: string
    }
  }>
  items: Array<{
    id: string
    bucketListItem: {
      id: string
      title: string
      category: string
      isCompleted: boolean
    }
  }>
  _count: {
    members: number
    items: number
  }
}

interface SocialState {
  // Discovery
  popularItems: DiscoveryItem[]
  recommendations: DiscoveryItem[]
  categories: Array<{
    category: string
    itemCount: number
    averageCompletion: number
  }>
  discoveryLoading: boolean
  discoveryError: string | null

  // Shared Lists
  sharedLists: SharedList[]
  sharedListsLoading: boolean
  sharedListsError: string | null

  // Actions
  setPopularItems: (items: DiscoveryItem[]) => void
  setRecommendations: (items: DiscoveryItem[]) => void
  setCategories: (categories: Array<{ category: string; itemCount: number; averageCompletion: number }>) => void
  setDiscoveryLoading: (loading: boolean) => void
  setDiscoveryError: (error: string | null) => void

  setSharedLists: (lists: SharedList[]) => void
  addSharedList: (list: SharedList) => void
  updateSharedList: (id: string, updates: Partial<SharedList>) => void
  deleteSharedList: (id: string) => void
  setSharedListsLoading: (loading: boolean) => void
  setSharedListsError: (error: string | null) => void
}

export const useSocialStore = create<SocialState>()(
  devtools(
    (set) => ({
      // Discovery state
      popularItems: [],
      recommendations: [],
      categories: [],
      discoveryLoading: false,
      discoveryError: null,

      // Shared Lists state
      sharedLists: [],
      sharedListsLoading: false,
      sharedListsError: null,

      // Discovery actions
      setPopularItems: (items) => set({ popularItems: items }),
      setRecommendations: (items) => set({ recommendations: items }),
      setCategories: (categories) => set({ categories }),
      setDiscoveryLoading: (loading) => set({ discoveryLoading: loading }),
      setDiscoveryError: (error) => set({ discoveryError: error }),

      // Shared Lists actions
      setSharedLists: (lists) => set({ sharedLists: lists }),

      addSharedList: (list) => set((state) => ({
        sharedLists: [list, ...state.sharedLists]
      })),

      updateSharedList: (id, updates) => set((state) => ({
        sharedLists: state.sharedLists.map(list =>
          list.id === id ? { ...list, ...updates } : list
        )
      })),

      deleteSharedList: (id) => set((state) => ({
        sharedLists: state.sharedLists.filter(list => list.id !== id)
      })),

      setSharedListsLoading: (loading) => set({ sharedListsLoading: loading }),
      setSharedListsError: (error) => set({ sharedListsError: error })
    }),
    {
      name: 'social-store'
    }
  )
)