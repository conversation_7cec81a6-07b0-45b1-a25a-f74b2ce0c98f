'use client'

import { useState } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { 
  Users, 
  Search, 
  Heart, 
  MessageCircle, 
  Share2, 
  TrendingUp,
  Globe,
  UserPlus,
  Filter,
  MapPin,
  Calendar,
  Target,
  Star,
  Eye,
  ThumbsUp
} from 'lucide-react'

interface CommunityGoal {
  id: string
  title: string
  description: string
  category: string
  status: string
  location?: string
  author: {
    name: string
    avatar?: string
    verified: boolean
  }
  stats: {
    likes: number
    comments: number
    views: number
  }
  tags: string[]
  createdAt: string
  isLiked: boolean
  isPublic: boolean
}

// Mock data for demonstration
const mockCommunityGoals: CommunityGoal[] = [
  {
    id: '1',
    title: 'Hike the Inca Trail to Machu Picchu',
    description: 'Experience the ancient wonder of Machu Picchu by hiking the historic Inca Trail through the Andes mountains.',
    category: 'TRAVEL',
    status: 'COMPLETED',
    location: 'Peru',
    author: {
      name: 'Adventure Sarah',
      verified: true
    },
    stats: {
      likes: 127,
      comments: 23,
      views: 1205
    },
    tags: ['hiking', 'adventure', 'history', 'mountains'],
    createdAt: '2024-01-15',
    isLiked: false,
    isPublic: true
  },
  {
    id: '2',
    title: 'Learn to play jazz piano',
    description: 'Master the art of jazz piano improvisation and perform at a local jazz club.',
    category: 'LEARNING',
    status: 'IN_PROGRESS',
    author: {
      name: 'Music Mike',
      verified: false
    },
    stats: {
      likes: 89,
      comments: 15,
      views: 743
    },
    tags: ['music', 'piano', 'jazz', 'performance'],
    createdAt: '2024-02-03',
    isLiked: true,
    isPublic: true
  },
  {
    id: '3',
    title: 'Start a sustainable garden',
    description: 'Create a thriving organic garden that provides fresh vegetables year-round while supporting local wildlife.',
    category: 'PERSONAL',
    status: 'PLAYING',
    author: {
      name: 'Green Thumb Emma',
      verified: true
    },
    stats: {
      likes: 156,
      comments: 31,
      views: 892
    },
    tags: ['gardening', 'sustainability', 'organic', 'environment'],
    createdAt: '2024-01-28',
    isLiked: false,
    isPublic: true
  }
]

export default function CommunityPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [activeTab, setActiveTab] = useState('discover')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLAYING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'PAUSED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleLike = (goalId: string) => {
    // TODO: Implement like functionality
    console.log('Like goal:', goalId)
  }

  const handleComment = (goalId: string) => {
    // TODO: Implement comment functionality
    console.log('Comment on goal:', goalId)
  }

  const handleShare = (goalId: string) => {
    // TODO: Implement share functionality
    console.log('Share goal:', goalId)
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Users className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold">Community</h1>
              <p className="text-muted-foreground">
                Discover and share bucket list goals with fellow dreamers
              </p>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="discover">Discover</TabsTrigger>
            <TabsTrigger value="following">Following</TabsTrigger>
            <TabsTrigger value="trending">Trending</TabsTrigger>
          </TabsList>

          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search community goals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <Button variant="outline" size="sm">
                <Globe className="h-4 w-4 mr-2" />
                Location
              </Button>
            </div>
          </div>

          <TabsContent value="discover" className="space-y-6">
            {/* Community Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">2,847</div>
                  <div className="text-sm text-muted-foreground">Public Goals</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">1,293</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">856</div>
                  <div className="text-sm text-muted-foreground">Active Users</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">47</div>
                  <div className="text-sm text-muted-foreground">Countries</div>
                </CardContent>
              </Card>
            </div>

            {/* Community Goals */}
            <div className="space-y-4">
              {mockCommunityGoals.map((goal) => (
                <Card key={goal.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                          {goal.author.name.charAt(0)}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{goal.author.name}</span>
                            {goal.author.verified && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(goal.createdAt)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(goal.status)}>
                          {goal.status.replace('_', ' ')}
                        </Badge>
                        <Badge variant="outline">
                          {goal.category}
                        </Badge>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h3 className="text-xl font-semibold mb-2">{goal.title}</h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {goal.description}
                      </p>
                    </div>

                    {goal.location && (
                      <div className="flex items-center gap-2 mb-4 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        {goal.location}
                      </div>
                    )}

                    {goal.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {goal.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-6 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {goal.stats.views}
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-4 w-4" />
                          {goal.stats.likes}
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="h-4 w-4" />
                          {goal.stats.comments}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLike(goal.id)}
                          className={goal.isLiked ? 'text-red-500' : ''}
                        >
                          <Heart className={`h-4 w-4 mr-1 ${goal.isLiked ? 'fill-current' : ''}`} />
                          Like
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleComment(goal.id)}
                        >
                          <MessageCircle className="h-4 w-4 mr-1" />
                          Comment
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleShare(goal.id)}
                        >
                          <Share2 className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="following" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Following</CardTitle>
                <CardDescription>
                  Goals from people you follow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  <UserPlus className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="mb-2">You're not following anyone yet</p>
                  <p className="text-sm mb-4">Follow other users to see their goals here</p>
                  <Button variant="outline">
                    <Users className="h-4 w-4 mr-2" />
                    Discover People
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trending" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Trending Goals</CardTitle>
                <CardDescription>
                  Most popular goals this week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="mb-2">Trending goals coming soon!</p>
                  <p className="text-sm">We're working on algorithms to surface the most inspiring goals</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Share Your Journey</CardTitle>
            <CardDescription>
              Inspire others by making your goals public and sharing your progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="text-center md:text-left">
                <p className="text-sm text-muted-foreground">
                  When you create goals, you can choose to make them public and inspire others in the community.
                </p>
              </div>
              <Button onClick={() => router.push('/goals/new')}>
                <Target className="h-4 w-4 mr-2" />
                Create Public Goal
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
