'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { BucketListForm } from '@/components/bucket-list/bucket-list-form'
import { ArrowLeft, Plus } from 'lucide-react'

export default function NewGoalPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [loading, setLoading] = useState(false)

  const handleSave = async (data: any) => {
    try {
      setLoading(true)

      const goalData = {
        title: data.title.trim(),
        description: data.description?.trim() || undefined,
        category: data.category?.toUpperCase() as 'TRAVEL' | 'CAREER' | 'PERSONAL' | 'RELATIONSHIPS' | 'ADVENTURES' | 'LEARNING',
        priority: data.priority as 'LOW' | 'MEDIUM' | 'HIGH',
        target_date: data.target_date || undefined,
        location: data.location?.trim() || undefined,
        estimated_cost: data.estimated_cost ? parseFloat(data.estimated_cost) : undefined,
        notes: data.notes?.trim() || undefined,
        is_public: data.is_public || false,
        status: 'PLAYING' as const,
      }

      await bucketListApi.create(goalData)
      router.push('/dashboard')
    } catch (err) {
      if (err instanceof ApiError) {
        throw new Error(err.message)
      } else {
        throw new Error('Failed to create goal')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleBack = () => {
    router.back()
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="mb-6 p-6 glass rounded-xl border-0 shadow-xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-4 hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>

          <div className="flex items-center gap-4 mb-2">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
              <Plus className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold gradient-text">Add New Goal</h1>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Create a new bucket list item to track your dreams and aspirations.
          </p>
        </div>

        <BucketListForm
          onSave={handleSave}
          onCancel={handleBack}
          loading={loading}
          className="glass card-hover border-0 shadow-xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl"
        />
      </div>
    </AppLayout>
  )
}
