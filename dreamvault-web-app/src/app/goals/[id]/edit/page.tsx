'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { BucketListForm } from '@/components/bucket-list/bucket-list-form'
import { ArrowLeft, Loader2 } from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'



export default function EditGoalPage() {
  const { user } = useAuth()
  const router = useRouter()
  const params = useParams()
  const goalId = params.id as string

  const [goal, setGoal] = useState<BucketListItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load goal details
  useEffect(() => {
    const loadGoal = async () => {
      try {
        setLoading(true)
        setError(null)
        const goalData = await bucketListApi.getById(goalId)
        setGoal(goalData.item)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load goal details')
        }
        console.error('Error loading goal:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user && goalId) {
      loadGoal()
    }
  }, [user, goalId])

  const handleSave = async (data: any) => {
    try {
      setSaving(true)

      const updateData = {
        title: data.title.trim(),
        description: data.description?.trim() || undefined,
        category: data.category?.toUpperCase() as 'TRAVEL' | 'CAREER' | 'PERSONAL' | 'RELATIONSHIPS' | 'ADVENTURES' | 'LEARNING',
        priority: data.priority as 'LOW' | 'MEDIUM' | 'HIGH',
        status: data.status as 'PLAYING' | 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED',
        target_date: data.target_date || undefined,
        location: data.location?.trim() || undefined,
        estimated_cost: data.estimated_cost ? parseFloat(data.estimated_cost) : undefined,
        notes: data.notes?.trim() || undefined,
        is_public: data.is_public || false,
      }

      await bucketListApi.update(goalId, updateData)
      router.push('/dashboard')
    } catch (err) {
      if (err instanceof ApiError) {
        throw new Error(err.message)
      } else {
        throw new Error('Failed to update goal')
      }
    } finally {
      setSaving(false)
    }
  }

  const handleBack = () => {
    router.push('/dashboard')
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <div className="text-lg">Loading goal details...</div>
            </div>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (error && !goal) {
    return (
      <AppLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error}</div>
            <Button onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>

          <h1 className="text-3xl font-bold gradient-text">Edit Goal</h1>
          <p className="text-muted-foreground mt-2">Update your bucket list goal details</p>
        </div>

        {goal && (
          <BucketListForm
            item={goal}
            onSave={handleSave}
            onCancel={handleBack}
            loading={saving}
            className="glass card-hover border-0 shadow-xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl"
          />
        )}
      </div>
    </AppLayout>
  )
}
