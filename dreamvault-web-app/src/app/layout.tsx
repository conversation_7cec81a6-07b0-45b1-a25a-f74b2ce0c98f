import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/components/auth/auth-provider'
import { QueryProvider } from '@/components/providers/query-provider'
import { Toaster } from 'sonner'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'DreamVault - Your Bucket List Companion',
  description: 'Track, achieve, and celebrate your bucket list goals with DreamVault. Turn your dreams into reality with our comprehensive goal tracking platform.',
  keywords: 'bucket list, goals, dreams, achievements, tracking, personal development',
  authors: [{ name: 'DreamVault Team' }],
  creator: 'DreamVault',
  publisher: 'DreamVault',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002'),
  openGraph: {
    title: 'DreamVault - Your Bucket List Companion',
    description: 'Track, achieve, and celebrate your bucket list goals with DreamVault.',
    url: '/',
    siteName: 'DreamVault',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DreamVault - Your Bucket List Companion',
    description: 'Track, achieve, and celebrate your bucket list goals with DreamVault.',
    creator: '@dreamvault',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <QueryProvider>
          <AuthProvider>
            {children}
            <Toaster />
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  )
}
