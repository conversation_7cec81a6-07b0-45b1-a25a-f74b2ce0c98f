'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { AppLayout } from '@/components/layout/app-layout'
import { useAuth } from '@/components/auth/auth-provider'
import { bucketListApi, ApiError } from '@/lib/api'
import { 
  TrendingUp, 
  Target, 
  Calendar, 
  Trophy, 
  Clock,
  CheckCircle,
  PlayCircle,
  PauseCircle,
  Plus,
  Camera,
  FileText
} from 'lucide-react'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

export default function ProgressPage() {
  const { user } = useAuth()
  const router = useRouter()

  const [bucketListItems, setBucketListItems] = useState<BucketListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load bucket list items from API
  useEffect(() => {
    const loadBucketListItems = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await bucketListApi.getAll()
        setBucketListItems(response.items)
      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message)
        } else {
          setError('Failed to load progress data')
        }
        console.error('Error loading bucket list items:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      loadBucketListItems()
    }
  }, [user])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PLAYING': return <PlayCircle className="h-4 w-4" />
      case 'IN_PROGRESS': return <Clock className="h-4 w-4" />
      case 'COMPLETED': return <CheckCircle className="h-4 w-4" />
      case 'PAUSED': return <PauseCircle className="h-4 w-4" />
      default: return <Target className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLAYING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'PAUSED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Calculate progress statistics
  const stats = {
    total: bucketListItems.length,
    playing: bucketListItems.filter(item => item.status === 'PLAYING').length,
    inProgress: bucketListItems.filter(item => item.status === 'IN_PROGRESS').length,
    completed: bucketListItems.filter(item => item.status === 'COMPLETED').length,
    paused: bucketListItems.filter(item => item.status === 'PAUSED').length,
  }

  const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0
  const activeGoals = stats.playing + stats.inProgress

  // Get recent activity (goals updated in last 30 days)
  const recentActivity = bucketListItems
    .filter(item => {
      const updatedDate = new Date(item.updated_at)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      return updatedDate > thirtyDaysAgo
    })
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    .slice(0, 5)

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold">Progress Tracking</h1>
              <p className="text-muted-foreground">
                Monitor your journey towards achieving your dreams
              </p>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="text-lg">Loading your progress...</div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error}</div>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Progress Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Overall Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold mb-2">{completionRate.toFixed(1)}%</div>
                  <Progress value={completionRate} className="mb-2" />
                  <p className="text-xs text-muted-foreground">
                    {stats.completed} of {stats.total} goals completed
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{activeGoals}</div>
                  <p className="text-xs text-muted-foreground">
                    Currently working on
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Completed</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
                  <p className="text-xs text-muted-foreground">
                    Dreams achieved
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{recentActivity.length}</div>
                  <p className="text-xs text-muted-foreground">
                    Goals updated
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Your latest progress updates and goal changes
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent activity</p>
                    <p className="text-sm">Start working on your goals to see progress here!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recentActivity.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => router.push(`/goals/${item.id}`)}
                      >
                        <div className="flex items-center gap-3">
                          {getStatusIcon(item.status)}
                          <div>
                            <div className="font-medium">{item.title}</div>
                            <div className="text-sm text-muted-foreground">
                              Updated {formatDate(item.updated_at)}
                            </div>
                          </div>
                        </div>
                        <Badge className={getStatusColor(item.status)}>
                          {item.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Progress by Category */}
            <Card>
              <CardHeader>
                <CardTitle>Progress by Category</CardTitle>
                <CardDescription>
                  See how you're doing across different areas of your life
                </CardDescription>
              </CardHeader>
              <CardContent>
                {stats.total === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No goals yet</p>
                    <p className="text-sm mb-4">Add some goals to see category progress!</p>
                    <Button onClick={() => router.push('/goals/new')}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Goal
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {['TRAVEL', 'CAREER', 'PERSONAL', 'RELATIONSHIPS', 'ADVENTURES', 'LEARNING'].map((category) => {
                      const categoryItems = bucketListItems.filter(item => item.category === category)
                      const categoryCompleted = categoryItems.filter(item => item.status === 'COMPLETED').length
                      const categoryProgress = categoryItems.length > 0 ? (categoryCompleted / categoryItems.length) * 100 : 0

                      if (categoryItems.length === 0) return null

                      return (
                        <div key={category} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-medium">{category}</span>
                            <span className="text-muted-foreground">
                              {categoryCompleted}/{categoryItems.length} completed
                            </span>
                          </div>
                          <Progress value={categoryProgress} />
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Enhanced Progress Tracking Features */}
            <Card>
              <CardHeader>
                <CardTitle>Enhanced Progress Tracking</CardTitle>
                <CardDescription>
                  Advanced features to help you track your journey
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-6 border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer">
                    <Camera className="h-8 w-8 mx-auto mb-3 text-green-600" />
                    <h3 className="font-medium mb-2">Photo Progress</h3>
                    <p className="text-sm text-muted-foreground">
                      Upload photos to document your journey
                    </p>
                    <div className="mt-3">
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Available Now</span>
                    </div>
                  </div>
                  <div className="text-center p-6 border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer">
                    <FileText className="h-8 w-8 mx-auto mb-3 text-blue-600" />
                    <h3 className="font-medium mb-2">Progress Entries</h3>
                    <p className="text-sm text-muted-foreground">
                      Log detailed progress updates and milestones
                    </p>
                    <div className="mt-3">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Available Now</span>
                    </div>
                  </div>
                  <div className="text-center p-6 border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer">
                    <Trophy className="h-8 w-8 mx-auto mb-3 text-yellow-600" />
                    <h3 className="font-medium mb-2">Milestones</h3>
                    <p className="text-sm text-muted-foreground">
                      Set and celebrate important milestones
                    </p>
                    <div className="mt-3">
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Available Now</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
