'use client'

import { useEffect, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/components/auth/auth-provider'
import type { BucketListItem } from '@/components/bucket-list/bucket-list-item'

interface RealtimeBucketListHookProps {
  onItemCreated?: (item: BucketListItem) => void
  onItemUpdated?: (item: BucketListItem) => void
  onItemDeleted?: (itemId: string) => void
}

export function useRealtimeBucketList({
  onItemCreated,
  onItemUpdated,
  onItemDeleted
}: RealtimeBucketListHookProps) {
  const { user } = useAuth()
  const supabase = createSupabaseClient()

  const handleRealtimeEvent = useCallback((payload: any) => {
    console.log('Real-time event received:', payload)

    // Only process events for the current user
    if (!user || payload.new?.user_id !== user.id) {
      return
    }

    switch (payload.eventType) {
      case 'INSERT':
        if (onItemCreated && payload.new) {
          onItemCreated(payload.new as BucketListItem)
        }
        break
      
      case 'UPDATE':
        if (onItemUpdated && payload.new) {
          onItemUpdated(payload.new as BucketListItem)
        }
        break
      
      case 'DELETE':
        if (onItemDeleted && payload.old) {
          onItemDeleted(payload.old.id)
        }
        break
    }
  }, [user, onItemCreated, onItemUpdated, onItemDeleted])

  useEffect(() => {
    if (!user) return

    console.log('Setting up real-time subscription for bucket list items')

    // Subscribe to bucket list changes
    const channel = supabase
      .channel('bucket_list_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bucket_list_items',
          filter: `user_id=eq.${user.id}`
        },
        handleRealtimeEvent
      )
      .subscribe((status) => {
        console.log('Real-time subscription status:', status)
      })

    // Cleanup subscription on unmount
    return () => {
      console.log('Cleaning up real-time subscription')
      supabase.removeChannel(channel)
    }
  }, [user, supabase, handleRealtimeEvent])

  return {
    // Return any utility functions if needed
    isConnected: true // Could track actual connection status
  }
}

// Hook for real-time media file updates
export function useRealtimeMediaFiles(itemId?: string) {
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!itemId) return

    console.log('Setting up real-time subscription for media files')

    // Subscribe to media file changes for this item
    const channel = supabase
      .channel(`media_files_${itemId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'media_files',
          filter: `bucket_list_item_id=eq.${itemId}`
        },
        (payload) => {
          console.log('Media file real-time event:', payload)
          // Could emit custom events or call callbacks here
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [itemId, supabase])
}

// Hook for real-time progress updates
export function useRealtimeProgress(itemId?: string) {
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!itemId) return

    console.log('Setting up real-time subscription for progress updates')

    // Subscribe to progress changes for this item
    const channel = supabase
      .channel(`progress_${itemId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'progress_entries',
          filter: `bucket_list_item_id=eq.${itemId}`
        },
        (payload) => {
          console.log('Progress real-time event:', payload)
          // Could emit custom events or call callbacks here
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [itemId, supabase])
}
