'use client'

import { useUIStore } from '@/lib/store'
import { useEffect } from 'react'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

export function Toaster() {
  const { notifications, removeNotification } = useUIStore()

  useEffect(() => {
    notifications.forEach((notification) => {
      if (notification.duration && notification.duration > 0) {
        const timer = setTimeout(() => {
          removeNotification(notification.id)
        }, notification.duration)

        return () => clearTimeout(timer)
      }
    })
  }, [notifications, removeNotification])

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={cn(
            "rounded-lg border p-4 shadow-lg transition-all duration-300 ease-in-out",
            "bg-background text-foreground",
            {
              "border-green-200 bg-green-50 text-green-800": notification.type === 'success',
              "border-red-200 bg-red-50 text-red-800": notification.type === 'error',
              "border-yellow-200 bg-yellow-50 text-yellow-800": notification.type === 'warning',
              "border-blue-200 bg-blue-50 text-blue-800": notification.type === 'info',
            }
          )}
        >
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <h4 className="font-semibold text-sm">{notification.title}</h4>
              <p className="text-sm opacity-90 mt-1">{notification.message}</p>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="text-current opacity-50 hover:opacity-100 transition-opacity"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}