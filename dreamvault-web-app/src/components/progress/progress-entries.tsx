'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Calendar, TrendingUp, Camera, FileText, Target } from 'lucide-react'
import { MediaUpload } from '@/components/media-upload'
import { motion } from 'framer-motion'

interface ProgressEntry {
  id: string
  title: string
  description?: string
  progress_percentage: number
  created_at: string
  updated_at: string
  media_files?: MediaFile[]
}

interface MediaFile {
  id: string
  file_path: string
  caption?: string
  created_at: string
}

interface ProgressEntriesProps {
  bucketListItemId: string
  currentProgress?: number
  onProgressUpdate?: (newProgress: number) => void
}

export function ProgressEntries({ bucketListItemId, currentProgress = 0, onProgressUpdate }: ProgressEntriesProps) {
  const [entries, setEntries] = useState<ProgressEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newEntry, setNewEntry] = useState({
    title: '',
    description: '',
    progress_percentage: currentProgress,
  })
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    loadProgressEntries()
  }, [bucketListItemId])

  const loadProgressEntries = async () => {
    try {
      setLoading(true)
      // This would connect to your progress API
      // For now, using mock data
      const mockEntries: ProgressEntry[] = [
        {
          id: '1',
          title: 'Started planning the trip',
          description: 'Researched destinations and created initial itinerary',
          progress_percentage: 25,
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          title: 'Booked flights and accommodation',
          description: 'Found great deals and secured all bookings',
          progress_percentage: 60,
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        },
      ]
      setEntries(mockEntries)
    } catch (error) {
      console.error('Failed to load progress entries:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddEntry = async () => {
    if (!newEntry.title.trim()) return

    setSubmitting(true)
    try {
      // This would connect to your progress API
      const entry: ProgressEntry = {
        id: Date.now().toString(),
        title: newEntry.title,
        description: newEntry.description,
        progress_percentage: newEntry.progress_percentage,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      setEntries(prev => [entry, ...prev])
      onProgressUpdate?.(newEntry.progress_percentage)
      
      setNewEntry({
        title: '',
        description: '',
        progress_percentage: currentProgress,
      })
      setShowAddDialog(false)
    } catch (error) {
      console.error('Failed to add progress entry:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 50) return 'text-blue-600'
    if (percentage >= 25) return 'text-yellow-600'
    return 'text-gray-600'
  }

  const getProgressBg = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-100 dark:bg-green-900/20'
    if (percentage >= 50) return 'bg-blue-100 dark:bg-blue-900/20'
    if (percentage >= 25) return 'bg-yellow-100 dark:bg-yellow-900/20'
    return 'bg-gray-100 dark:bg-gray-900/20'
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Progress Tracking
            </CardTitle>
            <CardDescription>
              Document your journey with detailed progress entries
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Progress
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add Progress Entry</DialogTitle>
                <DialogDescription>
                  Record your progress and update your completion percentage
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Progress Title</Label>
                  <Input
                    id="title"
                    value={newEntry.title}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="What progress did you make?"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={newEntry.description}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your progress in detail..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="percentage">Progress Percentage</Label>
                  <div className="space-y-2">
                    <Input
                      id="percentage"
                      type="number"
                      min="0"
                      max="100"
                      value={newEntry.progress_percentage}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, progress_percentage: parseInt(e.target.value) || 0 }))}
                    />
                    <Progress value={newEntry.progress_percentage} className="h-2" />
                  </div>
                </div>
                <div>
                  <Label>Add Photos (Optional)</Label>
                  <MediaUpload
                    itemId={bucketListItemId}
                    onUploadComplete={(files) => {
                      console.log('Photos uploaded:', files)
                    }}
                    maxFiles={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddEntry} disabled={submitting || !newEntry.title.trim()}>
                  {submitting ? 'Adding...' : 'Add Progress'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : entries.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground/30 mb-4" />
            <p className="text-muted-foreground mb-2">No progress entries yet</p>
            <p className="text-sm text-muted-foreground mb-4">
              Start documenting your journey by adding your first progress entry
            </p>
            <Button onClick={() => setShowAddDialog(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add First Entry
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {entries.map((entry, index) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-lg border ${getProgressBg(entry.progress_percentage)}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{entry.title}</h4>
                    {entry.description && (
                      <p className="text-sm text-muted-foreground mb-2">{entry.description}</p>
                    )}
                  </div>
                  <Badge variant="secondary" className={getProgressColor(entry.progress_percentage)}>
                    {entry.progress_percentage}%
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(entry.created_at).toLocaleDateString()}
                  </div>
                  {entry.media_files && entry.media_files.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Camera className="h-3 w-3" />
                      {entry.media_files.length} photo{entry.media_files.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
