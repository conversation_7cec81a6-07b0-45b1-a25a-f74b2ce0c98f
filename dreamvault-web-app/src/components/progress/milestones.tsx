'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Flag, CheckCircle, Circle, Calendar, Target, Trophy } from 'lucide-react'
import { motion } from 'framer-motion'

interface Milestone {
  id: string
  title: string
  description?: string
  is_completed: boolean
  completed_date?: string
  target_date?: string
  order_index: number
  created_at: string
  updated_at: string
}

interface MilestonesProps {
  bucketListItemId: string
  onMilestoneComplete?: (milestone: Milestone) => void
}

export function Milestones({ bucketListItemId, onMilestoneComplete }: MilestonesProps) {
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newMilestone, setNewMilestone] = useState({
    title: '',
    description: '',
    target_date: '',
  })
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    loadMilestones()
  }, [bucketListItemId])

  const loadMilestones = async () => {
    try {
      setLoading(true)
      // This would connect to your milestones API
      // For now, using mock data
      const mockMilestones: Milestone[] = [
        {
          id: '1',
          title: 'Research destinations',
          description: 'Look into different countries and cities to visit',
          is_completed: true,
          completed_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          order_index: 0,
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          title: 'Save money for the trip',
          description: 'Set aside $5000 for travel expenses',
          is_completed: false,
          target_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          order_index: 1,
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '3',
          title: 'Book flights and accommodation',
          description: 'Find the best deals and make reservations',
          is_completed: false,
          target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          order_index: 2,
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        },
      ]
      setMilestones(mockMilestones.sort((a, b) => a.order_index - b.order_index))
    } catch (error) {
      console.error('Failed to load milestones:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddMilestone = async () => {
    if (!newMilestone.title.trim()) return

    setSubmitting(true)
    try {
      // This would connect to your milestones API
      const milestone: Milestone = {
        id: Date.now().toString(),
        title: newMilestone.title,
        description: newMilestone.description,
        is_completed: false,
        target_date: newMilestone.target_date || undefined,
        order_index: milestones.length,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      setMilestones(prev => [...prev, milestone])
      
      setNewMilestone({
        title: '',
        description: '',
        target_date: '',
      })
      setShowAddDialog(false)
    } catch (error) {
      console.error('Failed to add milestone:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const toggleMilestone = async (milestone: Milestone) => {
    try {
      const updatedMilestone = {
        ...milestone,
        is_completed: !milestone.is_completed,
        completed_date: !milestone.is_completed ? new Date().toISOString() : undefined,
        updated_at: new Date().toISOString(),
      }

      setMilestones(prev => 
        prev.map(m => m.id === milestone.id ? updatedMilestone : m)
      )

      if (!milestone.is_completed) {
        onMilestoneComplete?.(updatedMilestone)
      }
    } catch (error) {
      console.error('Failed to update milestone:', error)
    }
  }

  const completedCount = milestones.filter(m => m.is_completed).length
  const totalCount = milestones.length
  const completionPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Flag className="h-5 w-5" />
              Milestones
            </CardTitle>
            <CardDescription>
              Break down your goal into smaller, achievable milestones
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Milestone
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Milestone</DialogTitle>
                <DialogDescription>
                  Create a milestone to track progress towards your goal
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="milestone-title">Milestone Title</Label>
                  <Input
                    id="milestone-title"
                    value={newMilestone.title}
                    onChange={(e) => setNewMilestone(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="What milestone do you want to achieve?"
                  />
                </div>
                <div>
                  <Label htmlFor="milestone-description">Description (Optional)</Label>
                  <Textarea
                    id="milestone-description"
                    value={newMilestone.description}
                    onChange={(e) => setNewMilestone(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe this milestone..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="milestone-date">Target Date (Optional)</Label>
                  <Input
                    id="milestone-date"
                    type="date"
                    value={newMilestone.target_date}
                    onChange={(e) => setNewMilestone(prev => ({ ...prev, target_date: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddMilestone} disabled={submitting || !newMilestone.title.trim()}>
                  {submitting ? 'Adding...' : 'Add Milestone'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {totalCount > 0 && (
          <div className="mt-4 p-4 bg-muted/30 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {completedCount} of {totalCount} completed
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse flex items-center space-x-3">
                <div className="w-5 h-5 bg-muted rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : milestones.length === 0 ? (
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-muted-foreground/30 mb-4" />
            <p className="text-muted-foreground mb-2">No milestones yet</p>
            <p className="text-sm text-muted-foreground mb-4">
              Break down your goal into smaller milestones to track progress
            </p>
            <Button onClick={() => setShowAddDialog(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add First Milestone
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                  milestone.is_completed 
                    ? 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800' 
                    : 'bg-background'
                }`}
              >
                <div className="flex items-start gap-3">
                  <button
                    onClick={() => toggleMilestone(milestone)}
                    className="mt-0.5 transition-colors"
                  >
                    {milestone.is_completed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <Circle className="h-5 w-5 text-muted-foreground hover:text-primary" />
                    )}
                  </button>
                  
                  <div className="flex-1">
                    <h4 className={`font-medium ${milestone.is_completed ? 'line-through text-muted-foreground' : ''}`}>
                      {milestone.title}
                    </h4>
                    {milestone.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {milestone.description}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                      {milestone.completed_date && (
                        <div className="flex items-center gap-1">
                          <Trophy className="h-3 w-3" />
                          Completed {new Date(milestone.completed_date).toLocaleDateString()}
                        </div>
                      )}
                      {milestone.target_date && !milestone.is_completed && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Target: {new Date(milestone.target_date).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
