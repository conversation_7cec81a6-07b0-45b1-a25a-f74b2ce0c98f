'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Database, CheckCircle, XCircle, Loader2, RefreshCw, Wifi, WifiOff } from 'lucide-react'
import { motion } from 'framer-motion'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message?: string
  duration?: number
}

export function DatabaseTest() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])
  const [overallStatus, setOverallStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle')

  const tests = [
    { name: 'Database Connection', endpoint: '/api/health/database' },
    { name: 'Authentication Service', endpoint: '/api/health/auth' },
    { name: 'Bucket List API', endpoint: '/api/health/bucketlist' },
    { name: 'Media Storage', endpoint: '/api/health/storage' },
    { name: 'AI Services', endpoint: '/api/health/ai' },
  ]

  const runTests = async () => {
    setTesting(true)
    setOverallStatus('testing')
    setResults([])

    const testResults: TestResult[] = []

    for (const test of tests) {
      const startTime = Date.now()
      
      // Add pending result
      const pendingResult: TestResult = { name: test.name, status: 'pending' }
      testResults.push(pendingResult)
      setResults([...testResults])

      try {
        // Simulate API call - in real implementation, this would call actual health endpoints
        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500))
        
        // Simulate random success/failure for demo
        const success = Math.random() > 0.2 // 80% success rate
        
        const duration = Date.now() - startTime
        const result: TestResult = {
          name: test.name,
          status: success ? 'success' : 'error',
          message: success 
            ? `Connected successfully in ${duration}ms`
            : 'Connection failed - please check configuration',
          duration
        }

        // Update the result
        testResults[testResults.length - 1] = result
        setResults([...testResults])

      } catch (error) {
        const duration = Date.now() - startTime
        const result: TestResult = {
          name: test.name,
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          duration
        }

        testResults[testResults.length - 1] = result
        setResults([...testResults])
      }
    }

    // Determine overall status
    const hasErrors = testResults.some(r => r.status === 'error')
    setOverallStatus(hasErrors ? 'error' : 'success')
    setTesting(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Testing...</Badge>
      case 'success':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Passed</Badge>
      case 'error':
        return <Badge variant="destructive">Failed</Badge>
    }
  }

  const getOverallStatusIcon = () => {
    switch (overallStatus) {
      case 'idle':
        return <Database className="h-6 w-6 text-muted-foreground" />
      case 'testing':
        return <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
      case 'success':
        return <Wifi className="h-6 w-6 text-green-500" />
      case 'error':
        return <WifiOff className="h-6 w-6 text-red-500" />
    }
  }

  const successCount = results.filter(r => r.status === 'success').length
  const totalTests = results.length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getOverallStatusIcon()}
              Database & Services Test
            </CardTitle>
            <CardDescription>
              Test connectivity to all backend services and APIs
            </CardDescription>
          </div>
          <Button 
            onClick={runTests} 
            disabled={testing}
            variant={overallStatus === 'success' ? 'outline' : 'default'}
          >
            {testing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Run Tests
              </>
            )}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Test Progress</span>
              <span className="text-sm text-muted-foreground">
                {successCount}/{totalTests} passed
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  overallStatus === 'success' ? 'bg-green-500' : 
                  overallStatus === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }`}
                style={{ width: `${(totalTests / tests.length) * 100}%` }}
              />
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {results.length === 0 ? (
          <div className="text-center py-8">
            <Database className="h-12 w-12 mx-auto text-muted-foreground/30 mb-4" />
            <p className="text-muted-foreground mb-2">No tests run yet</p>
            <p className="text-sm text-muted-foreground">
              Click "Run Tests" to check connectivity to all services
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {results.map((result, index) => (
              <motion.div
                key={result.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <div>
                    <h4 className="font-medium">{result.name}</h4>
                    {result.message && (
                      <p className="text-sm text-muted-foreground">{result.message}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {result.duration && (
                    <span className="text-xs text-muted-foreground">
                      {result.duration}ms
                    </span>
                  )}
                  {getStatusBadge(result.status)}
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {overallStatus === 'success' && (
          <Alert className="mt-4 border-green-200 bg-green-50 dark:bg-green-900/10">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800 dark:text-green-300">
              All services are connected and functioning properly!
            </AlertDescription>
          </Alert>
        )}

        {overallStatus === 'error' && (
          <Alert variant="destructive" className="mt-4">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Some services failed connectivity tests. Please check your configuration and try again.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
