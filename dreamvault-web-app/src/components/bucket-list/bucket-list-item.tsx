'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  CheckCircle, 
  Clock,
  MapPin,
  Calendar,
  Star,
  Image as ImageIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface BucketListItem {
  id: string
  user_id: string
  title: string
  description?: string
  category: 'TRAVEL' | 'CAREER' | 'PERSONAL' | 'RELATIONSHIPS' | 'ADVENTURES' | 'LEARNING'
  status: 'PLAYING' | 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH'
  target_date?: string
  completed_date?: string
  is_public: boolean
  location?: string
  estimated_cost?: number
  actual_cost?: number
  notes?: string
  tags?: string[]
  created_at: string
  updated_at: string
  media_files?: Array<{
    id: string
    file_path: string
    mime_type: string
    file_name: string
  }>
}

interface BucketListItemProps {
  item: BucketListItem
  onEdit?: (item: BucketListItem) => void
  onDelete?: (id: string) => void
  onStatusChange?: (id: string, status: BucketListItem['status']) => void
  className?: string
}

const categoryColors = {
  TRAVEL: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  CAREER: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  PERSONAL: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  RELATIONSHIPS: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
  ADVENTURES: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  LEARNING: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
}

const statusColors = {
  PLAYING: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  IN_PROGRESS: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  COMPLETED: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  PAUSED: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
}

const priorityColors = {
  LOW: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400',
  MEDIUM: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
  HIGH: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300',
}

export function BucketListItemComponent({ 
  item, 
  onEdit, 
  onDelete, 
  onStatusChange, 
  className 
}: BucketListItemProps) {
  const [showActions, setShowActions] = useState(false)

  const getStatusIcon = (status: BucketListItem['status']) => {
    switch (status) {
      case 'PLAYING':
        return <Clock className="h-4 w-4" />
      case 'IN_PROGRESS':
        return <Play className="h-4 w-4" />
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />
      case 'PAUSED':
        return <Pause className="h-4 w-4" />
    }
  }

  const getNextStatus = (currentStatus: BucketListItem['status']): BucketListItem['status'] => {
    switch (currentStatus) {
      case 'PLAYING':
        return 'IN_PROGRESS'
      case 'IN_PROGRESS':
        return 'COMPLETED'
      case 'PAUSED':
        return 'IN_PROGRESS'
      case 'COMPLETED':
        return 'PLAYING'
      default:
        return 'PLAYING'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card 
      className={cn(
        "group hover:shadow-lg transition-all duration-200 cursor-pointer",
        item.status === 'COMPLETED' && "ring-2 ring-green-200 dark:ring-green-800",
        className
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge className={categoryColors[item.category]}>
                {item.category}
              </Badge>
              <Badge className={statusColors[item.status]}>
                {getStatusIcon(item.status)}
                <span className="ml-1 capitalize">{item.status.replace('_', ' ')}</span>
              </Badge>
              {item.priority !== 'LOW' && (
                <Badge className={priorityColors[item.priority]}>
                  <Star className="h-3 w-3 mr-1" />
                  {item.priority}
                </Badge>
              )}
            </div>
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {item.title}
            </CardTitle>
            {item.description && (
              <CardDescription className="mt-1 line-clamp-2">
                {item.description}
              </CardDescription>
            )}
          </div>
          
          <div className={cn(
            "flex items-center gap-1 transition-opacity duration-200",
            showActions ? "opacity-100" : "opacity-0"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onStatusChange?.(item.id, getNextStatus(item.status))
              }}
              className="h-8 w-8 p-0"
            >
              {getStatusIcon(getNextStatus(item.status))}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onEdit?.(item)
              }}
              className="h-8 w-8 p-0"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onDelete?.(item.id)
              }}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Progress tracking can be added later with progress entries */}

        {/* Meta Information */}
        <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
          {item.location && (
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              <span>{item.location}</span>
            </div>
          )}
          {item.target_date && (
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(item.target_date)}</span>
            </div>
          )}
          {item.media_files && item.media_files.length > 0 && (
            <div className="flex items-center gap-1">
              <ImageIcon className="h-4 w-4" />
              <span>{item.media_files.length} photo{item.media_files.length !== 1 ? 's' : ''}</span>
            </div>
          )}
        </div>

        {/* Media Preview */}
        {item.media_files && item.media_files.length > 0 && (
          <div className="mt-3 flex gap-2">
            {item.media_files.slice(0, 3).map((media) => (
              <div
                key={media.id}
                className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center overflow-hidden"
              >
                {media.mime_type.startsWith('image/') ? (
                  <img
                    src={media.file_path}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <ImageIcon className="h-6 w-6 text-gray-400" />
                )}
              </div>
            ))}
            {item.media_files.length > 3 && (
              <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-xs text-gray-600 dark:text-gray-400">
                +{item.media_files.length - 3}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
