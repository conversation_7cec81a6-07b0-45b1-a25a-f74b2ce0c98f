'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Loader2,
  Save,
  X,
  Calendar,
  MapPin,
  Target,
  Star,
  Upload,
  Image as ImageIcon,
  Sparkles,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { BucketListItem } from './bucket-list-item'
import { MediaUpload } from '@/components/media-upload'

interface BucketListFormProps {
  item?: Partial<BucketListItem>
  onSave: (data: Partial<BucketListItem>) => Promise<void>
  onCancel: () => void
  loading?: boolean
  className?: string
}

export function BucketListForm({
  item,
  onSave,
  onCancel,
  loading = false,
  className
}: BucketListFormProps) {
  const [formData, setFormData] = useState({
    title: item?.title || '',
    description: item?.description || '',
    category: item?.category || 'personal',
    status: item?.status || 'planning',
    priority: item?.priority || 'medium',
    target_date: item?.target_date ? item.target_date.split('T')[0] : '',
    location: item?.location || '',
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  // AI Suggestions state
  const [aiSuggestions, setAiSuggestions] = useState<{
    suggestedCategory?: string
    suggestedTags: string[]
    extractedLocations: string[]
    isContentAppropriate: boolean
  }>({
    suggestedTags: [],
    extractedLocations: [],
    isContentAppropriate: true,
  })
  const [aiLoading, setAiLoading] = useState(false)
  const [showAiSuggestions, setShowAiSuggestions] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const categories = [
    { value: 'travel', label: 'Travel', icon: '✈️' },
    { value: 'career', label: 'Career', icon: '💼' },
    { value: 'personal', label: 'Personal', icon: '🌟' },
    { value: 'relationships', label: 'Relationships', icon: '❤️' },
    { value: 'adventures', label: 'Adventures', icon: '🏔️' },
    { value: 'learning', label: 'Learning', icon: '📚' },
  ]

  const statuses = [
    { value: 'planning', label: 'Planning', icon: '📋' },
    { value: 'in_progress', label: 'In Progress', icon: '🚀' },
    { value: 'completed', label: 'Completed', icon: '✅' },
    { value: 'paused', label: 'Paused', icon: '⏸️' },
  ]

  const priorities = [
    { value: 'LOW', label: 'Low Priority', icon: '🔵' },
    { value: 'MEDIUM', label: 'Medium Priority', icon: '🟡' },
    { value: 'HIGH', label: 'High Priority', icon: '🔴' },
  ]

  // AI suggestions effect
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Only trigger AI suggestions if title is long enough and it's a new item
    if (!item?.id && formData.title && formData.title.trim().length > 3) {
      // Debounce the AI call by 1 second to prevent overloading
      timeoutRef.current = setTimeout(() => {
        getAiSuggestions()
      }, 1000)
    } else {
      // Reset suggestions if title is too short
      setShowAiSuggestions(false)
      setAiSuggestions({
        suggestedTags: [],
        extractedLocations: [],
        isContentAppropriate: true,
      })
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [formData.title, formData.description, item?.id])

  const getAiSuggestions = async () => {
    if (!formData.title || !formData.title.trim()) return

    // Prevent multiple concurrent requests
    if (aiLoading) return

    setAiLoading(true)
    try {
      const response = await fetch('/api/ai/enhance-goal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title.trim(),
          description: formData.description?.trim() || ''
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get AI suggestions')
      }

      const result = await response.json()

      // Ensure we have valid data structure with fallbacks
      const safeSuggestions = {
        suggestedCategory: result?.suggestedCategory || undefined,
        suggestedTags: Array.isArray(result?.suggestedTags) ? result.suggestedTags : [],
        extractedLocations: Array.isArray(result?.extractedLocations) ? result.extractedLocations : [],
        isContentAppropriate: result?.isContentAppropriate !== false, // Default to true if undefined
      }

      setAiSuggestions(safeSuggestions)
      setShowAiSuggestions(true)
    } catch (error) {
      console.error('Failed to get AI suggestions:', error)
      // Reset to safe defaults on error
      setAiSuggestions({
        suggestedTags: [],
        extractedLocations: [],
        isContentAppropriate: true,
      })
    } finally {
      setAiLoading(false)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters'
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    if (formData.target_date) {
      const targetDate = new Date(formData.target_date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (targetDate < today) {
        newErrors.target_date = 'Target date cannot be in the past'
      }
    }

    if (formData.location && formData.location.length > 100) {
      newErrors.location = 'Location must be less than 100 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)

    if (!validateForm()) {
      return
    }

    try {
      const submitData = {
        ...formData,
        target_date: formData.target_date ? new Date(formData.target_date).toISOString() : undefined,
        category: formData.category as 'TRAVEL' | 'CAREER' | 'PERSONAL' | 'RELATIONSHIPS' | 'ADVENTURES' | 'LEARNING',
        status: formData.status as 'PLAYING' | 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED',
        priority: formData.priority as 'LOW' | 'MEDIUM' | 'HIGH',
      }

      await onSave(submitData)
      setMessage({ type: 'success', text: 'Goal saved successfully!' })
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to save goal' 
      })
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          {item?.id ? 'Edit Goal' : 'Add New Goal'}
        </CardTitle>
        <CardDescription>
          {item?.id 
            ? 'Update your bucket list item details'
            : 'Create a new item for your bucket list'
          }
        </CardDescription>
      </CardHeader>

      <CardContent>
        {message && (
          <Alert className={`mb-6 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="What do you want to achieve?"
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your goal in more detail..."
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* AI Suggestions */}
          {!item?.id && (showAiSuggestions || aiLoading) && (
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-4 w-4 text-blue-600" />
                    <CardTitle className="text-sm font-medium text-blue-700">AI Suggestions</CardTitle>
                    {aiLoading && <Loader2 className="h-4 w-4 animate-spin text-blue-600" />}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAiSuggestions(false)}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {!aiSuggestions.isContentAppropriate && (
                  <Alert>
                    <AlertDescription className="text-sm">
                      Content may need review. Please ensure it follows community guidelines.
                    </AlertDescription>
                  </Alert>
                )}

                {aiSuggestions.suggestedCategory && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Suggested Category</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleInputChange('category', aiSuggestions.suggestedCategory!.toLowerCase())}
                      className="h-8 text-xs border-blue-200 text-blue-700 hover:bg-blue-50"
                    >
                      <span>{aiSuggestions.suggestedCategory}</span>
                      <Plus className="ml-2 h-3 w-3" />
                    </Button>
                  </div>
                )}

                {aiSuggestions.suggestedTags && aiSuggestions.suggestedTags.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Suggested Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {aiSuggestions.suggestedTags.map((tag, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newDescription = formData.description + (formData.description ? ' ' : '') + tag
                            handleInputChange('description', newDescription)
                          }}
                          className="h-7 text-xs border-gray-200 text-gray-600 hover:bg-gray-50"
                        >
                          <span>{tag}</span>
                          <Plus className="ml-1 h-3 w-3" />
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {aiSuggestions.extractedLocations && aiSuggestions.extractedLocations.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Detected Locations</h4>
                    <div className="flex flex-wrap gap-2">
                      {aiSuggestions.extractedLocations.map((location, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleInputChange('location', location)}
                          className="h-7 text-xs border-green-200 text-green-700 hover:bg-green-50"
                        >
                          <MapPin className="mr-1 h-3 w-3" />
                          <span>{location}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {!aiLoading &&
                 aiSuggestions.suggestedTags.length === 0 &&
                 aiSuggestions.extractedLocations.length === 0 &&
                 !aiSuggestions.suggestedCategory && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">
                      No suggestions available. Try adding more details to your goal.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Category, Status, Priority Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      <span className="flex items-center gap-2">
                        <span>{category.icon}</span>
                        {category.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      <span className="flex items-center gap-2">
                        <span>{status.icon}</span>
                        {status.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorities.map(priority => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <span className="flex items-center gap-2">
                        <span>{priority.icon}</span>
                        {priority.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Target Date and Location Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="target_date">Target Date</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="target_date"
                  type="date"
                  value={formData.target_date}
                  onChange={(e) => handleInputChange('target_date', e.target.value)}
                  className={cn("pl-10", errors.target_date ? 'border-red-500' : '')}
                />
              </div>
              {errors.target_date && (
                <p className="text-sm text-red-600">{errors.target_date}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="Where will this happen?"
                  className={cn("pl-10", errors.location ? 'border-red-500' : '')}
                />
              </div>
              {errors.location && (
                <p className="text-sm text-red-600">{errors.location}</p>
              )}
            </div>
          </div>

          {/* Media Upload */}
          {item?.id && (
            <div className="space-y-2">
              <Label>Photos & Media</Label>
              <MediaUpload
                itemId={item.id}
                onUploadComplete={(files) => {
                  console.log('Files uploaded:', files)
                  // Handle uploaded files - could trigger a refresh or update state
                }}
                maxFiles={5}
                maxSize={50 * 1024 * 1024} // 50MB
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button type="submit" disabled={loading} className="flex-1">
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              {item?.id ? 'Update Goal' : 'Create Goal'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
